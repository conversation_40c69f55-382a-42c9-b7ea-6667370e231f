#!/bin/bash

# Dou-flow 完整版应用验证脚本

APP_NAME="Dou-flow.app"

echo "🔍 验证完整版 ${APP_NAME} 应用包..."

if [ ! -d "${APP_NAME}" ]; then
    echo "❌ 错误：找不到 ${APP_NAME}"
    exit 1
fi

echo "📦 应用包存在"
echo "📏 应用包大小: $(du -sh "${APP_NAME}" | cut -f1)"

echo ""
echo "🔍 检查关键文件..."

# 检查启动脚本
if [ -f "${APP_NAME}/Contents/MacOS/run.command" ]; then
    echo "✅ 启动脚本存在"
else
    echo "❌ 启动脚本缺失"
fi

# 检查主程序
if [ -f "${APP_NAME}/Contents/Resources/main.py" ]; then
    echo "✅ 主程序存在"
else
    echo "❌ 主程序缺失"
fi

# 检查AI模型
if [ -f "${APP_NAME}/Contents/Resources/modelscope/hub/damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch/model.pt" ]; then
    echo "✅ 语音识别模型存在"
else
    echo "❌ 语音识别模型缺失"
fi

if [ -f "${APP_NAME}/Contents/Resources/modelscope/hub/damo/punc_ct-transformer_zh-cn-common-vocab272727-pytorch/model.pt" ]; then
    echo "✅ 标点符号模型存在"
else
    echo "❌ 标点符号模型缺失"
fi

# 检查依赖文件
if [ -f "${APP_NAME}/Contents/Resources/requirements.txt" ]; then
    echo "✅ 依赖列表存在"
else
    echo "❌ 依赖列表缺失"
fi

# 检查UI文件
if [ -d "${APP_NAME}/Contents/Resources/ui" ]; then
    echo "✅ UI模块存在"
else
    echo "❌ UI模块缺失"
fi

# 检查安全管理器
if [ -f "${APP_NAME}/Contents/Resources/safe_hotkey_manager.py" ]; then
    echo "✅ 安全热键管理器存在"
else
    echo "❌ 安全热键管理器缺失"
fi

if [ -f "${APP_NAME}/Contents/Resources/safe_clipboard_manager.py" ]; then
    echo "✅ 安全剪贴板管理器存在"
else
    echo "❌ 安全剪贴板管理器缺失"
fi

echo ""
echo "📊 详细统计:"
echo "源代码文件数: $(find "${APP_NAME}/Contents/Resources" -name "*.py" | wc -l)"
echo "资源文件数: $(find "${APP_NAME}/Contents/Resources" -type f \( -name "*.png" -o -name "*.svg" -o -name "*.wav" -o -name "*.txt" \) | wc -l)"
echo "模型文件数: $(find "${APP_NAME}/Contents/Resources/modelscope" -name "*.pt" 2>/dev/null | wc -l)"

echo ""
echo "✅ 完整版应用验证完成！"
echo "🚀 应用可以直接分发使用"