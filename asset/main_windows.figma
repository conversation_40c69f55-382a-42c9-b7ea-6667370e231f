<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>Dou-flow v1.1.63</title>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<style type="text/tailwindcss">
        :root {
            --background-dark: #1E1E1E;
            --background-main: #2D2D2D;
            --header-bg: #1A1A1A;
            --text-primary: #E0E0E0;
            --text-secondary: #B0B0B0;
            --text-muted: #888888;
            --control-close: #FF5F56;
            --control-minimize: #FFBD2E;
            --control-maximize: #27C93F;
            --accent-green: #27C93F;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-dark);
        }
        .main-container {
            background-color: var(--background-main);
            color: var(--text-primary);
        }
        .header {
            background-color: var(--header-bg);
        }
        .window-controls span {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        .close { background-color: var(--control-close); }
        .minimize { background-color: var(--control-minimize); }
        .maximize { background-color: var(--control-maximize); }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen">
<div class="w-[800px] h-[600px] main-container rounded-lg shadow-2xl flex flex-col">
<header class="header px-4 py-3 flex items-center justify-between rounded-t-lg flex-shrink-0">
<div class="flex items-center space-x-2">
<div class="window-controls flex space-x-2">
<span class="close"></span>
<span class="minimize"></span>
<span class="maximize"></span>
</div>
</div>
<div class="flex items-center">
<span class="text-[var(--text-primary)] text-sm font-semibold">Dou-flow</span>
<span class="text-[var(--text-secondary)] text-xs ml-2">v1.1.63</span>
<span class="w-2 h-2 bg-[var(--accent-green)] rounded-full ml-2"></span>
</div>
<div class="flex items-center space-x-4">
<span class="material-icons text-[var(--text-secondary)] cursor-pointer">settings</span>
</div>
</header>
<main class="flex-grow p-6 space-y-4 overflow-y-auto">
<div class="flex justify-between items-start">
<p class="text-base leading-relaxed">可以新建一个完整的打包的脚本，用来打包给其他电脑用的。</p>
<div class="flex items-center ml-4 flex-shrink-0">
<span class="text-[var(--text-muted)] text-xs mr-3">00:45</span>
<button class="text-[var(--text-secondary)] hover:text-white transition-colors">
<span class="material-icons text-lg">content_copy</span>
</button>
</div>
</div>
<div class="flex justify-between items-start">
<p class="text-base leading-relaxed">这个表格吗？没动过。</p>
<div class="flex items-center ml-4 flex-shrink-0">
<span class="text-[var(--text-muted)] text-xs mr-3">00:45</span>
<button class="text-[var(--text-secondary)] hover:text-white transition-colors">
<span class="material-icons text-lg">content_copy</span>
</button>
</div>
</div>
<div class="flex justify-between items-start">
<p class="text-base leading-relaxed">现在会自动更新的是这三个。</p>
<div class="flex items-center ml-4 flex-shrink-0">
<span class="text-[var(--text-muted)] text-xs mr-3">00:45</span>
<button class="text-[var(--text-secondary)] hover:text-white transition-colors">
<span class="material-icons text-lg">content_copy</span>
</button>
</div>
</div>
<div class="flex justify-between items-start">
<p class="text-base leading-relaxed">其他的还需要手动更新。</p>
<div class="flex items-center ml-4 flex-shrink-0">
<span class="text-[var(--text-muted)] text-xs mr-3">00:45</span>
<button class="text-[var(--text-secondary)] hover:text-white transition-colors">
<span class="material-icons text-lg">content_copy</span>
</button>
</div>
</div>
<div class="flex justify-between items-start">
<p class="text-base leading-relaxed">而且只做增量更新。</p>
<div class="flex items-center ml-4 flex-shrink-0">
<span class="text-[var(--text-muted)] text-xs mr-3">00:45</span>
<button class="text-[var(--text-secondary)] hover:text-white transition-colors">
<span class="material-icons text-lg">content_copy</span>
</button>
</div>
</div>
<div class="flex justify-between items-start">
<p class="text-base leading-relaxed text-[var(--text-muted)]">...</p>
<div class="flex items-center ml-4 flex-shrink-0">
<span class="text-[var(--text-muted)] text-xs">00:45</span>
</div>
</div>
</main>
<footer class="p-6 flex justify-center items-center flex-shrink-0">
<button class="w-20 h-20 bg-black rounded-full flex items-center justify-center shadow-lg hover:bg-gray-800 transition-colors">
<span class="material-icons text-white text-4xl">mic</span>
</button>
</footer>
</div>

</body></html>
