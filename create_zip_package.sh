#!/bin/bash

# Dou-flow 完整版ZIP打包脚本
# 创建自包含应用的ZIP压缩包

set -e

APP_NAME="Dou-flow"
APP_VERSION="1.0.0"
ZIP_NAME="${APP_NAME}-Complete-${APP_VERSION}.zip"
APP_BUNDLE="${APP_NAME}.app"

echo "📦 开始创建完整版 ${APP_NAME} ZIP安装包..."

# 检查应用包是否存在
if [ ! -d "${APP_BUNDLE}" ]; then
    echo "❌ 错误：找不到 ${APP_BUNDLE}"
    echo "请先运行 ./build_standalone_app.sh 创建完整应用包"
    exit 1
fi

# 删除旧的ZIP文件
if [ -f "${ZIP_NAME}" ]; then
    echo "🧹 删除旧的ZIP文件..."
    rm -f "${ZIP_NAME}"
fi

# 获取应用包大小
APP_SIZE=$(du -sh "${APP_BUNDLE}" | cut -f1)
echo "📏 应用包大小: ${APP_SIZE}"

# 创建ZIP压缩包
echo "🗜️ 创建ZIP压缩包..."
zip -r "${ZIP_NAME}" "${APP_BUNDLE}" -x "*.DS_Store" "*/.git/*" "*/.__*" || {
    echo "❌ 创建ZIP失败，可能是磁盘空间不足"
    exit 1
}

# 检查ZIP文件是否创建成功
if [ -f "${ZIP_NAME}" ]; then
    ZIP_SIZE=$(du -sh "${ZIP_NAME}" | cut -f1)
    echo ""
    echo "✅ 完整版ZIP创建完成！"
    echo "📦 文件名：${ZIP_NAME}"
    echo "📏 原始大小：${APP_SIZE}"
    echo "📏 压缩后大小：${ZIP_SIZE}"
    echo "🎯 包含内容：完整源代码 + AI模型 + 所有依赖"
    echo "🎯 支持：Intel和Apple Silicon Mac (Universal)"
    echo ""
    echo "🚀 分发说明："
    echo "1. 将ZIP文件分享给用户"
    echo "2. 用户解压后得到 ${APP_BUNDLE}"
    echo "3. 将 ${APP_BUNDLE} 移动到应用程序文件夹"
    echo "4. 双击启动（首次运行会自动配置环境）"
else
    echo "❌ ZIP文件创建失败"
    exit 1
fi