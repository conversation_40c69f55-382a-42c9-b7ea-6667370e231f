#!/bin/bash

# Dou-flow 轻量级应用构建脚本
# 创建不包含AI模型的轻量应用包，模型首次使用时下载

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 配置变量
APP_NAME="Dou-flow"
APP_BUNDLE="${APP_NAME}.app"

print_info "=== 开始构建轻量级 ${APP_NAME}.app ==="

# 删除旧的应用包
if [ -d "${APP_BUNDLE}" ]; then
    print_info "删除旧的应用包..."
    rm -rf "${APP_BUNDLE}"
fi

# 创建应用包结构
print_info "创建应用包结构..."
mkdir -p "${APP_BUNDLE}/Contents/"{MacOS,Resources}

# 创建 Info.plist
print_info "创建 Info.plist..."
cat > "${APP_BUNDLE}/Contents/Info.plist" << EOL
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>run.command</string>
    <key>CFBundleIconFile</key>
    <string>app_icon</string>
    <key>CFBundleIdentifier</key>
    <string>com.ttmouse.douflow</string>
    <key>CFBundleName</key>
    <string>${APP_NAME}</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    <key>LSMinimumSystemVersion</key>
    <string>10.15</string>
    <key>LSArchitecturePriority</key>
    <array>
        <string>arm64</string>
        <string>x86_64</string>
    </array>
    <key>LSRequiresNativeExecution</key>
    <false/>
    <key>NSMicrophoneUsageDescription</key>
    <string>Dou-flow需要使用麦克风来录制音频进行语音识别</string>
    <key>NSAppleEventsUsageDescription</key>
    <string>Dou-flow需要访问系统事件以支持快捷键和自动粘贴功能</string>
    <key>NSAppleEventsEnabled</key>
    <true/>
    <key>NSAccessibilityUsageDescription</key>
    <string>Dou-flow需要辅助功能权限来支持自动粘贴和系统集成</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>LSApplicationCategoryType</key>
    <string>public.app-category.productivity</string>
</dict>
</plist>
EOL

# 创建 entitlements.plist
print_info "创建 entitlements.plist..."
cat > "${APP_BUNDLE}/Contents/entitlements.plist" << EOL
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>com.apple.security.automation.apple-events</key>
    <true/>
    <key>com.apple.security.device.microphone</key>
    <true/>
    <key>com.apple.security.device.audio-input</key>
    <true/>
    <key>com.apple.security.cs.allow-jit</key>
    <true/>
    <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
    <true/>
    <key>com.apple.security.cs.disable-library-validation</key>
    <true/>
    <key>com.apple.security.temporary-exception.apple-events</key>
    <array>
        <string>com.apple.systemevents</string>
        <string>com.apple.finder</string>
        <string>*</string>
    </array>
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>
    <key>com.apple.security.network.client</key>
    <true/>
</dict>
</plist>
EOL

# 复制所有源代码到应用包，保持src目录结构，但排除模型文件
print_info "复制源代码..."
mkdir -p "${APP_BUNDLE}/Contents/Resources/src"

# 复制源代码，但排除modelscope目录
rsync -av --exclude='modelscope/' src/ "${APP_BUNDLE}/Contents/Resources/src/"

# 复制专用的启动脚本
print_info "复制轻量版启动脚本..."
cp standalone_main.py "${APP_BUNDLE}/Contents/Resources/main.py"

# 复制资源文件
print_info "复制资源文件..."
if [ -d "resources" ]; then
    cp -R resources/ "${APP_BUNDLE}/Contents/Resources/"
fi

# 复制requirements.txt
print_info "复制依赖文件..."
if [ -f "requirements.txt" ]; then
    cp requirements.txt "${APP_BUNDLE}/Contents/Resources/"
fi

# 复制图标
print_info "复制应用图标..."
if [ -f "app_icon.icns" ]; then
    cp "app_icon.icns" "${APP_BUNDLE}/Contents/Resources/"
elif [ -f "iconset.icns" ]; then
    cp "iconset.icns" "${APP_BUNDLE}/Contents/Resources/app_icon.icns"
fi

# 创建模型目录结构（但不包含模型文件）
print_info "创建模型目录结构..."
mkdir -p "${APP_BUNDLE}/Contents/Resources/src/modelscope/hub/damo"

# 创建轻量级启动脚本
print_info "创建轻量级启动脚本..."
cat > "${APP_BUNDLE}/Contents/MacOS/run.command" << 'EOL'
#!/bin/bash

# 获取应用包路径
APP_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
RESOURCES_DIR="$APP_DIR/../Resources"
cd "$RESOURCES_DIR"

# 设置错误处理
set -e

# 显示用户友好的错误信息
show_error() {
    osascript -e "display dialog \"$1\" buttons {\"确定\"} default button \"确定\" with icon stop with title \"Dou-flow 启动错误\""
    exit 1
}

show_info() {
    osascript -e "display dialog \"$1\" buttons {\"确定\"} default button \"确定\" with icon note with title \"Dou-flow\""
}

show_progress() {
    osascript -e "display dialog \"$1\" buttons {} giving up after 3 with title \"Dou-flow\""
}

# 检查并下载模型
check_and_download_models() {
    # 检查模型是否存在
    ASR_MODEL_DIR="$RESOURCES_DIR/src/modelscope/hub/damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch"
    PUNC_MODEL_DIR="$RESOURCES_DIR/src/modelscope/hub/damo/punc_ct-transformer_zh-cn-common-vocab272727-pytorch"
    
    if [ ! -d "$ASR_MODEL_DIR" ] || [ ! -f "$ASR_MODEL_DIR/model.pt" ]; then
        show_info "首次运行需要下载AI模型，这可能需要10-15分钟时间，请保持网络连接..."
        
        # 检查网络连接
        if ! curl -s --connect-timeout 10 https://www.modelscope.cn > /dev/null; then
            show_error "网络连接失败，无法下载模型。请检查网络连接后重试。"
        fi
        
        # 检查git
        if ! command -v git >/dev/null 2>&1; then
            show_error "需要安装Git命令行工具。请安装Xcode Command Line Tools：
            
xcode-select --install"
        fi
        
        # 创建模型目录
        mkdir -p "$(dirname "$ASR_MODEL_DIR")"
        
        # 下载ASR模型
        if [ ! -d "$ASR_MODEL_DIR" ] || [ ! -f "$ASR_MODEL_DIR/model.pt" ]; then
            show_progress "正在下载语音识别模型（约1GB）..."
            if ! git clone --depth 1 https://www.modelscope.cn/iic/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch.git "$ASR_MODEL_DIR"; then
                show_error "下载语音识别模型失败，请检查网络连接"
            fi
        fi
        
        # 下载标点模型
        if [ ! -d "$PUNC_MODEL_DIR" ] || [ ! -f "$PUNC_MODEL_DIR/model.pt" ]; then
            show_progress "正在下载标点符号模型（约300MB）..."
            if ! git clone --depth 1 https://www.modelscope.cn/iic/punc_ct-transformer_zh-cn-common-vocab272727-pytorch.git "$PUNC_MODEL_DIR"; then
                show_error "下载标点符号模型失败，请检查网络连接"
            fi
        fi
        
        show_info "模型下载完成！应用即将启动。"
    fi
}

# 检查并安装conda环境
check_and_setup_conda() {
    # 设置conda路径
    export PATH="$HOME/miniconda3/bin:$HOME/anaconda3/bin:/opt/miniconda3/bin:/opt/anaconda3/bin:$PATH"
    
    # 初始化conda环境
    for conda_path in "$HOME/miniconda3" "$HOME/anaconda3" "/opt/miniconda3" "/opt/anaconda3"; do
        if [ -f "$conda_path/etc/profile.d/conda.sh" ]; then
            source "$conda_path/etc/profile.d/conda.sh"
            break
        fi
    done
    
    # 检查conda是否安装
    if ! command -v conda >/dev/null 2>&1; then
        show_error "未检测到conda环境。请先安装Miniconda或Anaconda：

1. 访问 https://docs.conda.io/en/latest/miniconda.html
2. 下载适合您系统的版本
3. 安装后重新启动此应用"
        return 1
    fi

    # 检查funasr_env环境是否存在
    if ! conda env list | grep -q "funasr_env"; then
        show_info "首次运行需要创建专用环境，这可能需要几分钟时间..."

        # 创建环境
        conda create -n funasr_env python=3.10 -y || show_error "创建conda环境失败"

        # 激活环境并安装依赖
        eval "$(conda shell.bash hook)"
        conda activate funasr_env
        
        # 安装依赖
        if [ -f "requirements.txt" ]; then
            pip install -r requirements.txt || show_error "安装项目依赖失败"
        fi

        show_info "环境创建完成！"
    fi
}

# 主要逻辑
main() {
    # 检查并下载模型
    check_and_download_models
    
    # 检查并设置环境
    check_and_setup_conda

    # 激活环境
    export PATH="$HOME/miniconda3/bin:$HOME/anaconda3/bin:/opt/miniconda3/bin:/opt/anaconda3/bin:$PATH"
    
    # 初始化conda环境
    for conda_path in "$HOME/miniconda3" "$HOME/anaconda3" "/opt/miniconda3" "/opt/anaconda3"; do
        if [ -f "$conda_path/etc/profile.d/conda.sh" ]; then
            source "$conda_path/etc/profile.d/conda.sh"
            break
        fi
    done
    
    # 激活funasr_env环境
    conda activate funasr_env || show_error "无法激活conda环境 funasr_env"

    # 检查Python环境
    if ! command -v python >/dev/null 2>&1; then
        show_error "Python环境异常，请重新安装conda环境"
    fi

    # 设置关键环境变量
    export DISABLE_INPUT_SOURCE_CHECK=1
    export LAUNCHED_FROM_APP_BUNDLE=1
    export PYTHONUNBUFFERED=1
    export QT_MAC_DISABLE_FOREGROUND_APPLICATION_TRANSFORM=1
    export MODELSCOPE_CACHE="$RESOURCES_DIR/src/modelscope/hub"

    # 运行程序
    python main.py 2>&1 | tee "$HOME/dou-flow-error.log"
}

main "$@"
EOL

# 设置执行权限
print_info "设置执行权限..."
chmod +x "${APP_BUNDLE}/Contents/MacOS/run.command"

# 移除扩展属性
print_info "清理扩展属性..."
xattr -cr "${APP_BUNDLE}"

# 计算应用包大小
APP_SIZE=$(du -sh "${APP_BUNDLE}" | cut -f1)

# 对应用进行签名
print_info "对应用进行代码签名..."
codesign --force --deep --sign - --entitlements "${APP_BUNDLE}/Contents/entitlements.plist" "${APP_BUNDLE}" || print_warning "代码签名失败，但应用仍可使用"

print_success "=== 轻量级应用构建完成！ ==="
echo ""
echo "📦 应用位置: ${APP_BUNDLE}"
echo "📏 应用大小: ${APP_SIZE}"
echo "🎯 特性: 轻量级，模型首次使用时下载"
echo "🚀 现在可以双击 ${APP_BUNDLE} 启动应用"
echo ""
print_info "💡 注意: 首次运行会自动下载AI模型（约1.3GB）"
