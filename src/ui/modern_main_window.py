"""
现代化主窗口 - 符合Apple设计规范的UI界面
采用现代macOS应用的视觉风格和交互模式
"""

from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QLabel, QListWidget, QListWidgetItem, QSystemTrayIcon, 
                            QMenu, QApplication, QDialog, QMenuBar, QFrame, QPushButton)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QPoint, QSettings, QEvent, QEasingCurve, QPropertyAnimation, QRect
from PyQt6.QtGui import QIcon, QFont, QAction, QKeySequence, QShortcut, QPainter, QColor, QBrush, QPen
from .modern_settings_window import ModernSettingsWindow
from .components.modern_button import ModernButton
from .components.modern_list_widget import ModernListWidget
from .components.history_manager import HistoryManager
from .simple_style_config import style_config
import os
import sys
import json
import re
from datetime import datetime
from config import APP_VERSION
from utils.text_utils import clean_html_tags

class ModernTitleBar(QWidget):
    """现代化标题栏 - 采用半透明毛玻璃效果"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(60)
        self.setup_ui()
        
    def setup_ui(self):
        """设置标题栏UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 0, 20, 0)
        layout.setSpacing(12)
        
        # 应用图标和标题组合
        title_container = QWidget()
        title_layout = QHBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(12)
        
        # 应用图标
        self.app_icon = QLabel("🎙")
        self.app_icon.setStyleSheet("""
            QLabel {
                font-size: 24px;
                background-color: rgba(0, 122, 255, 0.1);
                border-radius: 15px;
                padding: 6px;
                min-width: 30px;
                min-height: 30px;
                max-width: 30px;
                max-height: 30px;
            }
        """)
        self.app_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(self.app_icon)
        
        # 标题文字
        title_text_container = QWidget()
        title_text_layout = QVBoxLayout(title_text_container)
        title_text_layout.setContentsMargins(0, 8, 0, 8)
        title_text_layout.setSpacing(2)
        
        self.title_label = QLabel("Dou-flow")
        self.title_label.setStyleSheet("""
            QLabel {
                font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display";
                font-size: 17px;
                font-weight: 600;
                color: #1d1d1f;
                line-height: 1.2;
            }
        """)
        title_text_layout.addWidget(self.title_label)
        
        self.subtitle_label = QLabel("语音转文字助手")
        self.subtitle_label.setStyleSheet("""
            QLabel {
                font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text";
                font-size: 12px;
                font-weight: 400;
                color: #86868b;
                line-height: 1.2;
            }
        """)
        title_text_layout.addWidget(self.subtitle_label)
        
        title_layout.addWidget(title_text_container)
        layout.addWidget(title_container)
        
        # 状态指示器
        self.status_indicator = QLabel()
        self.status_indicator.setFixedSize(12, 12)
        self.status_indicator.setStyleSheet("""
            QLabel {
                background-color: #34c759;
                border-radius: 6px;
                border: 2px solid white;
            }
        """)
        self.status_indicator.setToolTip("就绪")
        layout.addWidget(self.status_indicator)
        
        # 弹性空间
        layout.addStretch()
        
        # 控制按钮
        self.setup_control_buttons(layout)
        
    def setup_control_buttons(self, layout):
        """设置控制按钮"""
        # 设置按钮
        self.settings_btn = self.create_control_button("⚙", "#86868b", "设置")
        self.settings_btn.clicked.connect(self.open_settings)
        layout.addWidget(self.settings_btn)
        
        # 最小化按钮
        self.minimize_btn = self.create_control_button("−", "#ffcc02", "最小化")
        self.minimize_btn.clicked.connect(self.minimize_window)
        layout.addWidget(self.minimize_btn)
        
        # 关闭按钮
        self.close_btn = self.create_control_button("×", "#ff3b30", "关闭")
        self.close_btn.clicked.connect(self.close_window)
        layout.addWidget(self.close_btn)
        
    def create_control_button(self, text, color, tooltip):
        """创建统一样式的控制按钮"""
        btn = QPushButton(text)
        btn.setFixedSize(28, 28)
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: rgba(134, 134, 139, 0.1);
                border: none;
                border-radius: 14px;
                font-size: 14px;
                font-weight: 500;
                color: #86868b;
            }}
            QPushButton:hover {{
                background-color: {color};
                color: white;
            }}
            QPushButton:pressed {{
                background-color: {color};
                transform: scale(0.95);
            }}
        """)
        btn.setToolTip(tooltip)
        return btn
        
    def open_settings(self):
        """打开设置"""
        if self.parent() and hasattr(self.parent(), 'open_settings'):
            self.parent().open_settings()
            
    def minimize_window(self):
        """最小化窗口"""
        if self.parent():
            self.parent().hide()
            
    def close_window(self):
        """关闭窗口"""
        if self.parent():
            self.parent().close()

class RecordingButton(QPushButton):
    """现代化录音按钮"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(60, 60)
        self.is_recording = False
        self.setup_ui()
        
    def setup_ui(self):
        """设置录音按钮UI"""
        self.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007AFF, stop:1 #0051D0);
                border: none;
                border-radius: 30px;
                font-size: 32px;
                color: white;
                box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0056D6, stop:1 #003DB8);
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF3B30, stop:1 #D70015);
                transform: scale(0.95);
            }
        """)
        self.setText("🎙")
        
    def set_recording_state(self, is_recording):
        """设置录音状态"""
        self.is_recording = is_recording
        if is_recording:
            self.setText("⏹")
            self.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #FF3B30, stop:1 #D70015);
                    border: none;
                    border-radius: 30px;
                    font-size: 32px;
                    color: white;
                    box-shadow: 0 4px 20px rgba(255, 59, 48, 0.3);
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #D70015, stop:1 #B8000D);
                    transform: scale(1.05);
                }
            """)
        else:
            self.setText("🎙")
            self.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #007AFF, stop:1 #0051D0);
                    border: none;
                    border-radius: 30px;
                    font-size: 32px;
                    color: white;
                    box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #0056D6, stop:1 #003DB8);
                    transform: scale(1.05);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #FF3B30, stop:1 #D70015);
                    transform: scale(0.95);
                }
            """)


class ModernMainWindow(QMainWindow):
    """现代化主窗口 - 符合Apple设计规范"""
    
    record_button_clicked = pyqtSignal()
    history_item_clicked = pyqtSignal(str)
    
    def __init__(self, app_instance=None):
        super().__init__()
        self.app_instance = app_instance
        self._initialization_complete = False
        self.setup_window()
        self.setup_ui()
        self.setup_interactions()
        self._initialization_complete = True
        
    def setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle("Dou-flow")
        self.setFixedSize(800, 500)  # 标准笔记本16:10宽高比
        
        # 设置窗口标志 - 去掉总是置顶
        self.setWindowFlags(
            Qt.WindowType.Window |
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowSystemMenuHint
        )
        
        # 设置窗口属性
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        
    def setup_ui(self):
        """设置UI界面"""
        # 创建主容器
        main_widget = QWidget()
        main_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-radius: 16px;
            }
        """)
        self.setCentralWidget(main_widget)
        
        # 主布局
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 标题栏
        self.title_bar = ModernTitleBar(self)
        main_layout.addWidget(self.title_bar)
        
        # 内容区域
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(24, 20, 24, 24)
        content_layout.setSpacing(24)
        
        # 状态显示区域
        self.setup_status_area(content_layout)
        
        # 录音按钮区域
        self.setup_recording_area(content_layout)
        
        # 历史记录区域
        self.setup_history_area(content_layout)
        
        main_layout.addWidget(content_widget)
        
        # 设置窗口图标
        self.load_window_icon()
        
    def setup_status_area(self, layout):
        """设置状态显示区域"""
        status_container = QFrame()
        status_container.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.8);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                padding: 16px;
            }
        """)
        status_container.setFixedHeight(80)
        
        status_layout = QHBoxLayout(status_container)
        status_layout.setContentsMargins(20, 16, 20, 16)
        
        # 状态文字
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text";
                font-size: 16px;
                font-weight: 500;
                color: #1d1d1f;
                background: transparent;
                border: none;
            }
        """)
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # 热键提示
        hotkey_label = QLabel("按住 fn 录音")
        hotkey_label.setStyleSheet("""
            QLabel {
                font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text";
                font-size: 14px;
                font-weight: 400;
                color: #86868b;
                background: rgba(134, 134, 139, 0.1);
                border-radius: 8px;
                padding: 6px 12px;
            }
        """)
        status_layout.addWidget(hotkey_label)
        
        layout.addWidget(status_container)
        
    def setup_recording_area(self, layout):
        """设置录音按钮区域"""
        recording_container = QWidget()
        recording_layout = QVBoxLayout(recording_container)
        recording_layout.setContentsMargins(0, 20, 0, 20)
        recording_layout.setSpacing(16)
        
        # 录音按钮
        self.record_button = RecordingButton()
        self.record_button.clicked.connect(self.on_record_button_clicked)
        recording_layout.addWidget(self.record_button, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 提示文字
        hint_label = QLabel("点击按钮或按住 fn 键开始录音")
        hint_label.setStyleSheet("""
            QLabel {
                font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text";
                font-size: 14px;
                font-weight: 400;
                color: #86868b;
                text-align: center;
            }
        """)
        hint_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        recording_layout.addWidget(hint_label)
        
        layout.addWidget(recording_container)
        
    def setup_history_area(self, layout):
        """设置历史记录区域"""
        # 标题
        history_title = QLabel("历史记录")
        history_title.setStyleSheet("""
            QLabel {
                font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display";
                font-size: 20px;
                font-weight: 600;
                color: #1d1d1f;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(history_title)
        
        # 历史记录列表
        self.history_list = ModernListWidget()
        self.history_list.itemClicked.connect(self.on_history_item_clicked)
        
        # 添加空状态提示
        self.setup_empty_state()
        
        layout.addWidget(self.history_list, 1)  # 设置拉伸因子
        
        # 初始化历史记录管理器
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.history_file = os.path.join(project_root, "history.json")
        self.history_manager = HistoryManager(self.history_file)
        
    def setup_empty_state(self):
        """设置空状态提示"""
        self.empty_state = QWidget()
        empty_layout = QVBoxLayout(self.empty_state)
        # 使用与列表项一致的边距
        margins = style_config.get_history_item_margins()
        empty_layout.setContentsMargins(
            margins.get('left', 16), 60, 
            margins.get('right', 16), 60
        )
        empty_layout.setSpacing(20)
        empty_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 图标
        icon_label = QLabel("🎙")
        icon_label.setStyleSheet("""
            QLabel {
                color: #86868b;
                font-size: 48px;
                font-weight: 300;
            }
        """)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        empty_layout.addWidget(icon_label)
        
        # 主要提示文字
        main_hint = QLabel("还没有录音记录")
        main_hint.setStyleSheet("""
            QLabel {
                font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display";
                font-size: 20px;
                font-weight: 600;
                color: #1d1d1f;
                text-align: center;
            }
        """)
        main_hint.setAlignment(Qt.AlignmentFlag.AlignCenter)
        empty_layout.addWidget(main_hint)
        
        # 辅助提示文字
        hint_label = QLabel("点击录音按钮或按住 fn 键开始录音\n松开后自动转换为文字")
        hint_label.setStyleSheet("""
            QLabel {
                font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text";
                font-size: 14px;
                font-weight: 400;
                color: #86868b;
                text-align: center;
                line-height: 1.5;
            }
        """)
        hint_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        empty_layout.addWidget(hint_label)
        
        # 将空状态提示添加到列表中
        self.history_list.setEmptyState(self.empty_state)
        
    def setup_interactions(self):
        """设置交互功能"""
        # 设置快捷键
        settings_shortcut = QShortcut(QKeySequence("Ctrl+,"), self)
        settings_shortcut.activated.connect(self.open_settings)
        
        if sys.platform == 'darwin':
            settings_shortcut_mac = QShortcut(QKeySequence("Meta+,"), self)
            settings_shortcut_mac.activated.connect(self.open_settings)
            
        # 拖拽功能
        self._is_dragging = False
        self._drag_start_pos = None
        
    def load_window_icon(self):
        """加载窗口图标"""
        try:
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            from resource_utils import get_icon_path
            icon = QIcon(get_icon_path("mic1.png"))
            self.setWindowIcon(icon)
        except Exception as e:
            print(f"加载图标失败: {e}")
            
    def set_state_manager(self, state_manager):
        """设置状态管理器"""
        self.state_manager = state_manager
        if state_manager:
            self.update_status("就绪")
            
    def load_history(self):
        """加载历史记录"""
        try:
            for item in self.history_manager.get_history_items_with_timestamps():
                text = item['text']
                timestamp = item['timestamp']
                
                # 将时间戳字符串转换为datetime对象（如果需要）
                if isinstance(timestamp, str) and timestamp:
                    try:
                        from datetime import datetime
                        timestamp_obj = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    except:
                        timestamp_obj = timestamp
                else:
                    timestamp_obj = timestamp
                
                # 使用带时间戳的addItem方法
                self.history_list.addItem(text, timestamp_obj)
        except Exception as e:
            print(f"加载历史记录失败: {e}")
            
    def add_to_history(self, text):
        """添加到历史记录"""
        if not text or not text.strip():
            return
            
        from utils.text_utils import clean_html_tags
        clean_text = clean_html_tags(text)
        
        if self.history_manager.add_history_item(clean_text):
            # 创建带时间戳的历史记录项
            from datetime import datetime
            timestamp = datetime.now().isoformat()
            
            # 创建自定义的历史记录项
            from .components.history_item import HistoryItemWidget
            list_item = QListWidgetItem()
            widget = HistoryItemWidget(clean_text, timestamp)
            
            self.history_list.addItem(list_item)
            self.history_list.setItemWidget(list_item, widget)
            list_item.setSizeHint(widget.sizeHint())
            
            self.history_list.scrollToBottom()
            
    def display_result(self, text, skip_history=False):
        """显示识别结果"""
        if text and text.strip() and not skip_history:
            self.add_to_history(text)
            
    def update_status(self, status):
        """更新状态显示"""
        self.status_label.setText(status)
        # 更新录音按钮状态
        if hasattr(self, 'record_button'):
            is_recording = status == "录音中"
            self.record_button.set_recording_state(is_recording)
        
    def on_record_button_clicked(self):
        """录音按钮点击事件"""
        self.record_button_clicked.emit()
        
    def on_history_item_clicked(self, item):
        """历史记录项点击事件"""
        text = item.text()
        self.history_item_clicked.emit(text)
        
    def open_settings(self):
        """打开设置窗口"""
        if self.app_instance and hasattr(self.app_instance, 'show_settings'):
            self.app_instance.show_settings()
        else:
            print("无法打开设置窗口：应用程序实例不可用")
            
    def mousePressEvent(self, event):
        """鼠标按下事件 - 实现窗口拖动"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 检查是否在标题栏区域
            title_bar_rect = self.title_bar.geometry()
            if title_bar_rect.contains(event.position().toPoint()):
                self._is_dragging = True
                self._drag_start_pos = event.position().toPoint()
                
    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 实现窗口拖动"""
        if self._is_dragging and event.buttons() == Qt.MouseButton.LeftButton:
            if self._drag_start_pos:
                delta = event.position().toPoint() - self._drag_start_pos
                self.move(self.pos() + delta)
                
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        self._is_dragging = False
        self._drag_start_pos = None
        
    def _show_window_internal(self):
        """在主线程中显示窗口"""
        try:
            if sys.platform == 'darwin':
                try:
                    from AppKit import NSApplication
                    app = NSApplication.sharedApplication()
                    
                    # 显示并激活窗口
                    if not self.isVisible():
                        self.show()
                    
                    # 强制激活应用和窗口
                    app.activateIgnoringOtherApps_(True)
                    self.raise_()
                    self.activateWindow()
                    self.setFocus(Qt.FocusReason.ActiveWindowFocusReason)
                    
                except Exception as e:
                    print(f"显示窗口时出错: {e}")
                    self._fallback_show_window()
            else:
                self._fallback_show_window()
                
        except Exception as e:
            print(f"显示窗口失败: {e}")
            
    def _fallback_show_window(self):
        """后备的窗口显示方法"""
        if not self.isVisible():
            self.show()
        self.raise_()
        self.activateWindow()
        self.setFocus(Qt.FocusReason.ActiveWindowFocusReason)
        
    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            if self.app_instance and hasattr(self.app_instance, 'quit_application'):
                self.app_instance.quit_application()
            else:
                QApplication.instance().quit()
            event.accept()
        except Exception as e:
            print(f"关闭窗口失败: {e}")
            QApplication.instance().quit()
            event.accept()