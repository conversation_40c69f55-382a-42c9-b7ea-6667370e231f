# 简化的样式配置类
# 提供基本的样式配置功能，不依赖复杂的配置管理器

import json
import os

class SimpleStyleConfig:
    """简化的样式配置类"""
    
    def __init__(self):
        self._config = self._load_default_config()
        self._load_config_file()
    
    def _load_default_config(self):
        """加载默认配置 - 真正的macOS设计规范"""
        return {
            "colors": {
                # macOS系统颜色 - 完全符合Apple HIG
                "background": "#FFFFFF",
                "secondary_background": "#F5F5F5",
                "card_background": "#FFFFFF",
                "text": "#000000",
                "secondary_text": "#6D6D6D",
                "tertiary_text": "#8E8E8E",
                "timestamp_text": "#8E8E8E",
                "border": "#E0E0E0",
                "separator": "#F0F0F0",
                # 交互状态颜色 - macOS系统蓝
                "hover": "#F0F8FF",
                "selected": "#0066CC",
                "selected_text": "#FFFFFF",
                # 滚动条 - macOS原生样式
                "scrollbar": "rgba(0,0,0,0.2)",
                "scrollbar_hover": "rgba(0,0,0,0.4)",
                # 阴影 - 更精细的macOS阴影
                "shadow": "rgba(0,0,0,0.04)",
                "card_shadow": "rgba(0,0,0,0.08)"
            },
            "fonts": {
                # macOS系统字体 - SF Pro
                "family": "SF Pro Text, -apple-system, BlinkMacSystemFont, PingFang SC, Helvetica Neue, Arial, sans-serif",
                "size": 14,
                "weight": "400",
                "timestamp_size": 12,
                "timestamp_weight": "400"
            },
            "spacing": {
                # macOS原生间距系统
                "list_item_padding": 20,
                "list_spacing": 1,
                "text_padding": 0,
                "text_margin": 0,
                "line_height": 1.5,
                "line_spacing_multiplier": 1.4,
                "timestamp_spacing": 6,
                "content_spacing": 8
            },
            "layout": {
                "history_item_margins": {
                    "left": 20,
                    "top": 16,
                    "right": 20,
                    "bottom": 16
                },
                "min_item_height": 60,
                "min_text_width": 320,
                "scrollbar_width": 8,
                "scrollbar_radius": 4,
                "min_handle_height": 30,
                "text_width_margin": 60,
                "corner_radius": 6,
                "shadow_blur": 8,
                "shadow_offset": 1,
                "card_padding": 16
            }
        }
    
    def _load_config_file(self):
        """从配置文件加载配置"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'styles_config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    self._merge_config(file_config)
        except Exception as e:
            import logging
            logging.error(f"加载样式配置文件失败: {e}")
    
    def _merge_config(self, file_config):
        """合并配置"""
        for section, values in file_config.items():
            if section in self._config:
                if isinstance(values, dict):
                    self._config[section].update(values)
                else:
                    self._config[section] = values
    
    def get_color(self, key):
        """获取颜色配置"""
        return self._config.get('colors', {}).get(key, '#000000')
    
    def get_font_family(self):
        """获取字体族"""
        return self._config.get('fonts', {}).get('family', 'PingFang SC')
    
    def get_font_size(self):
        """获取字体大小"""
        return self._config.get('fonts', {}).get('size', 14)
    
    def get_font_weight(self):
        """获取字体粗细"""
        return self._config.get('fonts', {}).get('weight', '400')
    
    def get_timestamp_font_size(self):
        """获取时间戳字体大小"""
        return self._config.get('fonts', {}).get('timestamp_size', 13)
    
    def get_timestamp_font_weight(self):
        """获取时间戳字体粗细"""
        return self._config.get('fonts', {}).get('timestamp_weight', '400')
    
    def get_spacing(self, key):
        """获取间距配置"""
        return self._config.get('spacing', {}).get(key, 0)
    
    def get_layout(self, key):
        """获取布局配置"""
        return self._config.get('layout', {}).get(key, {})
    
    def get_history_item_margins(self):
        """获取历史项边距配置"""
        return self._config.get('layout', {}).get('history_item_margins', {
            'left': 16,
            'top': 12,
            'right': 16,
            'bottom': 12
        })

# 创建全局实例
style_config = SimpleStyleConfig()