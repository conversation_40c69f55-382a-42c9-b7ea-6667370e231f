"""
macOS风格样式定义
遵循Apple Human Interface Guidelines
"""

from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QColor, QPalette
import platform

class MacOSStyle:
    """macOS设计系统样式配置"""
    
    # macOS系统字体
    SYSTEM_FONT = "SF Pro Display" if platform.system() == "Darwin" else "Segoe UI"
    SYSTEM_FONT_MONO = "SF Mono" if platform.system() == "Darwin" else "Consolas"
    
    # 字体大小 (遵循macOS Human Interface Guidelines)
    FONT_SIZE_LARGE = 20    # 大标题
    FONT_SIZE_TITLE = 17    # 标题
    FONT_SIZE_BODY = 13     # 正文
    FONT_SIZE_CAPTION = 11  # 说明文字
    FONT_SIZE_SMALL = 10    # 小字
    
    # macOS色彩系统
    COLORS_LIGHT = {
        'primary': '#007AFF',          # 系统蓝色
        'secondary': '#5856D6',        # 系统紫色
        'success': '#34C759',          # 系统绿色
        'warning': '#FF9500',          # 系统橙色
        'danger': '#FF3B30',           # 系统红色
        'background': '#FFFFFF',       # 主背景
        'background_secondary': '#F2F2F7',  # 次级背景
        'background_tertiary': '#FFFFFF',   # 三级背景
        'text_primary': '#000000',     # 主文字
        'text_secondary': '#3C3C43',   # 次级文字 (opacity 60%)
        'text_tertiary': '#3C3C43',    # 三级文字 (opacity 30%)
        'separator': '#3C3C43',        # 分隔线 (opacity 29%)
        'fill': '#78788033',           # 填充色
        'border': '#E5E5EA',           # 边框色
    }
    
    COLORS_DARK = {
        'primary': '#0A84FF',          # 系统蓝色 (深色)
        'secondary': '#5E5CE6',        # 系统紫色 (深色)
        'success': '#30D158',          # 系统绿色 (深色)
        'warning': '#FF9F0A',          # 系统橙色 (深色)
        'danger': '#FF453A',           # 系统红色 (深色)
        'background': '#000000',       # 主背景
        'background_secondary': '#1C1C1E',  # 次级背景
        'background_tertiary': '#2C2C2E',   # 三级背景
        'text_primary': '#FFFFFF',     # 主文字
        'text_secondary': '#EBEBF5',   # 次级文字 (opacity 60%)
        'text_tertiary': '#EBEBF5',    # 三级文字 (opacity 30%)
        'separator': '#54545899',      # 分隔线
        'fill': '#78788080',           # 填充色
        'border': '#38383A',           # 边框色
    }
    
    # 间距系统 (8pt grid)
    SPACING_TINY = 4
    SPACING_SMALL = 8
    SPACING_MEDIUM = 16
    SPACING_LARGE = 24
    SPACING_XLARGE = 32
    
    # 圆角半径
    BORDER_RADIUS_SMALL = 6
    BORDER_RADIUS_MEDIUM = 8
    BORDER_RADIUS_LARGE = 12
    BORDER_RADIUS_XLARGE = 16
    
    # 阴影
    SHADOW_ELEVATION_1 = "0px 1px 3px rgba(0, 0, 0, 0.12)"
    SHADOW_ELEVATION_2 = "0px 4px 12px rgba(0, 0, 0, 0.15)"
    SHADOW_ELEVATION_3 = "0px 8px 24px rgba(0, 0, 0, 0.20)"
    
    @classmethod
    def get_colors(cls, dark_mode=False):
        """获取当前主题色彩"""
        return cls.COLORS_DARK if dark_mode else cls.COLORS_LIGHT
    
    @classmethod
    def get_font(cls, size=None, weight=QFont.Weight.Normal):
        """获取系统字体"""
        font = QFont(cls.SYSTEM_FONT)
        if size:
            font.setPointSize(size)
        font.setWeight(weight)
        return font
    
    @classmethod
    def get_main_window_style(cls, dark_mode=False):
        """获取主窗口样式"""
        colors = cls.get_colors(dark_mode)
        
        return f"""
        QMainWindow {{
            background-color: {colors['background']};
            color: {colors['text_primary']};
            border: none;
        }}
        
        QWidget {{
            background-color: transparent;
            color: {colors['text_primary']};
            font-family: "{cls.SYSTEM_FONT}";
            font-size: {cls.FONT_SIZE_BODY}px;
        }}
        
        /* 标题栏背景 */
        QWidget#title_bar {{
            background-color: {colors['background_secondary']};
            border: none;
        }}
        
        /* 标题栏样式 */
        QLabel#title {{
            font-size: {cls.FONT_SIZE_TITLE}px;
            font-weight: 600;
            color: {colors['text_primary']};
            padding: {cls.SPACING_MEDIUM}px;
        }}
        
        QLabel#version_label {{
            font-size: {cls.FONT_SIZE_CAPTION}px;
            font-weight: 400;
            color: {colors['text_secondary']};
            padding: {cls.SPACING_MEDIUM}px;
        }}
        
        /* 录音按钮样式 */
        QPushButton#record_button {{
            background-color: {colors['primary']};
            color: white;
            border: none;
            border-radius: {cls.BORDER_RADIUS_LARGE}px;
            font-size: {cls.FONT_SIZE_BODY}px;
            font-weight: 500;
            padding: {cls.SPACING_SMALL}px {cls.SPACING_LARGE}px;
            min-height: 36px;
        }}
        
        QPushButton#record_button:hover {{
            background-color: {colors['primary']}DD;
        }}
        
        QPushButton#record_button:pressed {{
            background-color: {colors['primary']}BB;
        }}
        
        QPushButton#record_button:disabled {{
            background-color: {colors['fill']};
            color: {colors['text_tertiary']};
        }}
        
        /* 列表样式 */
        QListWidget {{
            background-color: {colors['background_secondary']};
            border: 1px solid {colors['border']};
            border-radius: {cls.BORDER_RADIUS_MEDIUM}px;
            outline: none;
            padding: {cls.SPACING_SMALL}px;
        }}
        
        QListWidget::item {{
            background-color: {colors['background']};
            border: none;
            border-radius: {cls.BORDER_RADIUS_SMALL}px;
            padding: {cls.SPACING_MEDIUM}px;
            margin: {cls.SPACING_TINY}px 0px;
        }}
        
        QListWidget::item:hover {{
            background-color: {colors['fill']};
        }}
        
        QListWidget::item:selected {{
            background-color: {colors['primary']}22;
            border: 1px solid {colors['primary']};
        }}
        
        /* 设置按钮样式 */
        QPushButton#settings_button {{
            background-color: transparent;
            border: 1px solid {colors['border']};
            border-radius: {cls.BORDER_RADIUS_SMALL}px;
            padding: {cls.SPACING_SMALL}px;
            min-width: 32px;
            min-height: 32px;
        }}
        
        QPushButton#settings_button:hover {{
            background-color: {colors['fill']};
        }}
        
        /* 状态标签样式 */
        QLabel#status_label {{
            color: {colors['text_secondary']};
            font-size: {cls.FONT_SIZE_CAPTION}px;
            padding: {cls.SPACING_SMALL}px {cls.SPACING_MEDIUM}px;
        }}
        """
    
    @classmethod
    def get_settings_window_style(cls, dark_mode=False):
        """获取设置窗口样式"""
        colors = cls.get_colors(dark_mode)
        
        return f"""
        QDialog {{
            background-color: {colors['background']};
            color: {colors['text_primary']};
        }}
        
        QTabWidget::pane {{
            border: 1px solid {colors['border']};
            border-radius: {cls.BORDER_RADIUS_MEDIUM}px;
            background-color: {colors['background']};
        }}
        
        QTabWidget::tab-bar {{
            alignment: center;
        }}
        
        QTabBar::tab {{
            background-color: {colors['background_secondary']};
            border: 1px solid {colors['border']};
            padding: {cls.SPACING_SMALL}px {cls.SPACING_LARGE}px;
            margin-right: 2px;
        }}
        
        QTabBar::tab:first {{
            border-top-left-radius: {cls.BORDER_RADIUS_SMALL}px;
            border-bottom-left-radius: {cls.BORDER_RADIUS_SMALL}px;
        }}
        
        QTabBar::tab:last {{
            border-top-right-radius: {cls.BORDER_RADIUS_SMALL}px;
            border-bottom-right-radius: {cls.BORDER_RADIUS_SMALL}px;
            margin-right: 0px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {colors['primary']};
            color: white;
        }}
        
        QTextEdit {{
            background-color: {colors['background_secondary']};
            border: 1px solid {colors['border']};
            border-radius: {cls.BORDER_RADIUS_SMALL}px;
            padding: {cls.SPACING_SMALL}px;
            font-family: "{cls.SYSTEM_FONT_MONO}";
        }}
        
        QPushButton {{
            background-color: {colors['primary']};
            color: white;
            border: none;
            border-radius: {cls.BORDER_RADIUS_SMALL}px;
            padding: {cls.SPACING_SMALL}px {cls.SPACING_LARGE}px;
            font-weight: 500;
        }}
        
        QPushButton:hover {{
            background-color: {colors['primary']}DD;
        }}
        
        QPushButton:pressed {{
            background-color: {colors['primary']}BB;
        }}
        """