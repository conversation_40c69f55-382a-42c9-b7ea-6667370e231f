"""
现代化设置窗口 - 符合Apple设计规范
采用现代macOS设置界面的视觉风格和布局
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QSlider, QComboBox, QCheckBox, QPushButton, 
                            QWidget, QFrame, QScrollArea, QGroupBox, QTabWidget)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QIcon

class ModernSectionWidget(QFrame):
    """现代化设置分组组件"""
    
    def __init__(self, title, parent=None):
        super().__init__(parent)
        self.setup_ui(title)
        
    def setup_ui(self, title):
        """设置UI"""
        self.setStyleSheet("""
            QFrame {
                background: white;
                border: 1px solid #e5e5e7;
                border-radius: 12px;
                margin: 4px 0px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 16, 20, 16)
        layout.setSpacing(16)
        
        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display";
                font-size: 17px;
                font-weight: 600;
                color: #1d1d1f;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(title_label)
        
        # 内容容器
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(12)
        layout.addWidget(self.content_widget)
        
    def add_setting_row(self, label_text, widget):
        """添加设置行"""
        row_widget = QWidget()
        row_layout = QHBoxLayout(row_widget)
        row_layout.setContentsMargins(0, 8, 0, 8)
        row_layout.setSpacing(16)
        
        # 标签
        label = QLabel(label_text)
        label.setStyleSheet("""
            QLabel {
                font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text";
                font-size: 14px;
                font-weight: 400;
                color: #1d1d1f;
                min-width: 120px;
            }
        """)
        row_layout.addWidget(label)
        
        # 控件
        row_layout.addWidget(widget)
        row_layout.addStretch()
        
        self.content_layout.addWidget(row_widget)

class ModernSlider(QSlider):
    """现代化滑块组件"""
    
    def __init__(self, orientation=Qt.Orientation.Horizontal, parent=None):
        super().__init__(orientation, parent)
        self.setup_style()
        
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QSlider::groove:horizontal {
                border: none;
                height: 4px;
                background: #e5e5e7;
                border-radius: 2px;
            }
            QSlider::handle:horizontal {
                background: #007AFF;
                border: none;
                width: 20px;
                height: 20px;
                margin: -8px 0;
                border-radius: 10px;
            }
            QSlider::handle:horizontal:hover {
                background: #0056D6;
                width: 22px;
                height: 22px;
                margin: -9px 0;
                border-radius: 11px;
            }
            QSlider::sub-page:horizontal {
                background: #007AFF;
                border-radius: 2px;
            }
        """)

class ModernComboBox(QComboBox):
    """现代化下拉框组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_style()
        
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QComboBox {
                background: #f2f2f7;
                border: 1px solid #d1d1d6;
                border-radius: 8px;
                padding: 8px 12px;
                font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text";
                font-size: 14px;
                color: #1d1d1f;
                min-width: 120px;
            }
            QComboBox:hover {
                border-color: #007AFF;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #86868b;
                margin-right: 8px;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #d1d1d6;
                border-radius: 8px;
                background: white;
                selection-background-color: #007AFF;
                selection-color: white;
                padding: 4px;
            }
        """)

class ModernCheckBox(QCheckBox):
    """现代化复选框组件"""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setup_style()
        
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QCheckBox {
                font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text";
                font-size: 14px;
                color: #1d1d1f;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 4px;
                border: 2px solid #d1d1d6;
                background: white;
            }
            QCheckBox::indicator:hover {
                border-color: #007AFF;
            }
            QCheckBox::indicator:checked {
                background: #007AFF;
                border-color: #007AFF;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEwIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik04LjUgMUwzLjUgNkwxLjUgNCIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
            }
        """)

class ModernButton(QPushButton):
    """现代化按钮组件"""
    
    def __init__(self, text="", button_type="primary", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.setup_style()
        
    def setup_style(self):
        """设置样式"""
        if self.button_type == "primary":
            self.setStyleSheet("""
                QPushButton {
                    background: #007AFF;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 20px;
                    font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text";
                    font-size: 14px;
                    font-weight: 500;
                    color: white;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background: #0056D6;
                }
                QPushButton:pressed {
                    background: #003DB8;
                }
            """)
        elif self.button_type == "secondary":
            self.setStyleSheet("""
                QPushButton {
                    background: #f2f2f7;
                    border: 1px solid #d1d1d6;
                    border-radius: 8px;
                    padding: 10px 20px;
                    font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text";
                    font-size: 14px;
                    font-weight: 500;
                    color: #1d1d1f;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background: #e5e5e7;
                    border-color: #007AFF;
                }
                QPushButton:pressed {
                    background: #d1d1d6;
                }
            """)

class ModernSettingsWindow(QDialog):
    """现代化设置窗口"""
    
    settings_changed = pyqtSignal()
    
    def __init__(self, parent=None, settings_manager=None, audio_capture=None):
        super().__init__(parent)
        self.settings_manager = settings_manager
        self.audio_capture = audio_capture
        self.setup_window()
        self.setup_ui()
        self.load_settings()
        
    def setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle("设置")
        self.setFixedSize(600, 700)
        self.setModal(True)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QDialog {
                background: #f8f9fa;
            }
        """)
        
    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 标题栏
        self.setup_title_bar(layout)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: transparent;
                width: 8px;
                border-radius: 4px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: rgba(134, 134, 139, 0.3);
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(134, 134, 139, 0.5);
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)
        
        # 内容区域
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(24, 20, 24, 20)
        content_layout.setSpacing(20)
        
        # 音频设置
        self.setup_audio_settings(content_layout)
        
        # 热键设置
        self.setup_hotkey_settings(content_layout)
        
        # 识别设置
        self.setup_recognition_settings(content_layout)
        
        # 高级设置
        self.setup_advanced_settings(content_layout)
        
        content_layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
        
        # 底部按钮
        self.setup_bottom_buttons(layout)
        
    def setup_title_bar(self, layout):
        """设置标题栏"""
        title_bar = QFrame()
        title_bar.setStyleSheet("""
            QFrame {
                background: white;
                border-bottom: 1px solid #e5e5e7;
                min-height: 60px;
                max-height: 60px;
            }
        """)
        
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(24, 16, 24, 16)
        
        # 标题
        title_label = QLabel("设置")
        title_label.setStyleSheet("""
            QLabel {
                font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display";
                font-size: 20px;
                font-weight: 600;
                color: #1d1d1f;
            }
        """)
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # 关闭按钮
        close_btn = QPushButton("×")
        close_btn.setFixedSize(28, 28)
        close_btn.setStyleSheet("""
            QPushButton {
                background: #e5e5e7;
                border: none;
                border-radius: 14px;
                font-size: 16px;
                color: #86868b;
            }
            QPushButton:hover {
                background: #ff3b30;
                color: white;
            }
        """)
        close_btn.clicked.connect(self.close)
        title_layout.addWidget(close_btn)
        
        layout.addWidget(title_bar)
        
    def setup_audio_settings(self, layout):
        """设置音频设置区域"""
        audio_section = ModernSectionWidget("音频设置")
        
        # 音量阈值
        self.volume_slider = ModernSlider()
        self.volume_slider.setRange(1, 100)
        self.volume_slider.setValue(10)
        audio_section.add_setting_row("音量阈值", self.volume_slider)
        
        # 录音设备
        self.device_combo = ModernComboBox()
        self.device_combo.addItems(["默认设备", "MacBook Pro麦克风"])
        audio_section.add_setting_row("录音设备", self.device_combo)
        
        # 最大录音时长
        self.duration_slider = ModernSlider()
        self.duration_slider.setRange(5, 60)
        self.duration_slider.setValue(20)
        audio_section.add_setting_row("最大录音时长 (秒)", self.duration_slider)
        
        layout.addWidget(audio_section)
        
    def setup_hotkey_settings(self, layout):
        """设置热键设置区域"""
        hotkey_section = ModernSectionWidget("热键设置")
        
        # 热键选择
        self.hotkey_combo = ModernComboBox()
        self.hotkey_combo.addItems(["fn", "ctrl", "alt"])
        hotkey_section.add_setting_row("录音热键", self.hotkey_combo)
        
        # 启动延迟
        self.delay_slider = ModernSlider()
        self.delay_slider.setRange(0, 500)
        self.delay_slider.setValue(50)
        hotkey_section.add_setting_row("启动延迟 (毫秒)", self.delay_slider)
        
        layout.addWidget(hotkey_section)
        
    def setup_recognition_settings(self, layout):
        """设置识别设置区域"""
        recognition_section = ModernSectionWidget("识别设置")
        
        # 自动标点
        self.auto_punct_check = ModernCheckBox("自动添加标点符号")
        recognition_section.content_layout.addWidget(self.auto_punct_check)
        
        # 实时显示
        self.realtime_check = ModernCheckBox("实时显示识别结果")
        recognition_section.content_layout.addWidget(self.realtime_check)
        
        # 发音纠错
        self.correction_check = ModernCheckBox("启用发音相似词纠错")
        recognition_section.content_layout.addWidget(self.correction_check)
        
        layout.addWidget(recognition_section)
        
    def setup_advanced_settings(self, layout):
        """设置高级设置区域"""
        advanced_section = ModernSectionWidget("高级设置")
        
        # 转录延迟
        self.transcription_delay_slider = ModernSlider()
        self.transcription_delay_slider.setRange(0, 1000)
        self.transcription_delay_slider.setValue(0)
        advanced_section.add_setting_row("转录延迟 (毫秒)", self.transcription_delay_slider)
        
        # 历史点击延迟
        self.history_delay_slider = ModernSlider()
        self.history_delay_slider.setRange(0, 500)
        self.history_delay_slider.setValue(0)
        advanced_section.add_setting_row("历史点击延迟 (毫秒)", self.history_delay_slider)
        
        layout.addWidget(advanced_section)
        
    def setup_bottom_buttons(self, layout):
        """设置底部按钮"""
        button_frame = QFrame()
        button_frame.setStyleSheet("""
            QFrame {
                background: white;
                border-top: 1px solid #e5e5e7;
                min-height: 70px;
                max-height: 70px;
            }
        """)
        
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(24, 16, 24, 16)
        button_layout.setSpacing(12)
        
        button_layout.addStretch()
        
        # 取消按钮
        cancel_btn = ModernButton("取消", "secondary")
        cancel_btn.clicked.connect(self.close)
        button_layout.addWidget(cancel_btn)
        
        # 保存按钮
        save_btn = ModernButton("保存", "primary")
        save_btn.clicked.connect(self.save_settings)
        button_layout.addWidget(save_btn)
        
        layout.addWidget(button_frame)
        
    def load_settings(self):
        """加载设置"""
        if not self.settings_manager:
            return
            
        try:
            # 加载音频设置
            volume_threshold = self.settings_manager.get_setting('audio.volume_threshold', 0.01)
            self.volume_slider.setValue(int(volume_threshold * 1000))  # 转换为 0-100 范围
            
            max_duration = self.settings_manager.get_setting('audio.max_recording_duration', 20)
            self.duration_slider.setValue(max_duration)
            
            # 加载热键设置
            hotkey = self.settings_manager.get_setting('hotkey', 'fn')
            index = self.hotkey_combo.findText(hotkey)
            if index >= 0:
                self.hotkey_combo.setCurrentIndex(index)
                
            delay = self.settings_manager.get_setting('hotkey_settings.recording_start_delay', 50)
            self.delay_slider.setValue(delay)
            
            # 加载识别设置
            auto_punct = self.settings_manager.get_setting('asr.auto_punctuation', False)
            self.auto_punct_check.setChecked(auto_punct)
            
            realtime = self.settings_manager.get_setting('asr.real_time_display', True)
            self.realtime_check.setChecked(realtime)
            
            correction = self.settings_manager.get_setting('asr.enable_pronunciation_correction', True)
            self.correction_check.setChecked(correction)
            
            # 加载高级设置
            trans_delay = self.settings_manager.get_setting('paste.transcription_delay', 0)
            self.transcription_delay_slider.setValue(trans_delay)
            
            history_delay = self.settings_manager.get_setting('paste.history_click_delay', 0)
            self.history_delay_slider.setValue(history_delay)
            
        except Exception as e:
            print(f"加载设置失败: {e}")
            
    def save_settings(self):
        """保存设置"""
        if not self.settings_manager:
            return
            
        try:
            # 保存音频设置
            volume_threshold = self.volume_slider.value() / 1000.0  # 转换回小数
            self.settings_manager.set_setting('audio.volume_threshold', volume_threshold)
            self.settings_manager.set_setting('audio.max_recording_duration', self.duration_slider.value())
            
            # 保存热键设置
            self.settings_manager.set_setting('hotkey', self.hotkey_combo.currentText())
            self.settings_manager.set_setting('hotkey_settings.recording_start_delay', self.delay_slider.value())
            
            # 保存识别设置
            self.settings_manager.set_setting('asr.auto_punctuation', self.auto_punct_check.isChecked())
            self.settings_manager.set_setting('asr.real_time_display', self.realtime_check.isChecked())
            self.settings_manager.set_setting('asr.enable_pronunciation_correction', self.correction_check.isChecked())
            
            # 保存高级设置
            self.settings_manager.set_setting('paste.transcription_delay', self.transcription_delay_slider.value())
            self.settings_manager.set_setting('paste.history_click_delay', self.history_delay_slider.value())
            
            # 保存到文件
            self.settings_manager.save_settings()
            
            # 发送设置变更信号
            self.settings_changed.emit()
            
            self.close()
            
        except Exception as e:
            print(f"保存设置失败: {e}")