#!/usr/bin/env python3
"""
模型下载对话框
提供用户友好的模型下载界面，包括进度显示、取消功能等
"""

import sys
import os
import threading
import time
from typing import Optional, Callable, Dict, Any
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QProgressBar, QTextEdit, QFrame,
    QApplication, QMessageBox
)
from PyQt6.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt6.QtGui import QFont, QPixmap, QIcon

try:
    from ..model_downloader import ModelDownloader
except ImportError:
    from model_downloader import ModelDownloader


class ModelDownloadWorker(QThread):
    """模型下载工作线程"""
    
    progress_updated = pyqtSignal(str, int, str)  # model_type, progress, message
    download_completed = pyqtSignal(str, bool)    # model_type, success
    all_completed = pyqtSignal(bool)              # overall_success
    error_occurred = pyqtSignal(str)              # error_message
    
    def __init__(self, models_to_download: list, base_dir: Optional[str] = None):
        super().__init__()
        self.models_to_download = models_to_download
        self.base_dir = base_dir
        self.should_cancel = False
        self.downloader = None
    
    def run(self):
        """运行下载任务"""
        try:
            # 创建下载器
            self.downloader = ModelDownloader(
                base_dir=self.base_dir,
                progress_callback=self._progress_callback
            )
            
            overall_success = True
            
            for model_type in self.models_to_download:
                if self.should_cancel:
                    break
                
                success = self.downloader.download_model(model_type)
                self.download_completed.emit(model_type, success)
                
                if not success:
                    overall_success = False
            
            if not self.should_cancel:
                self.all_completed.emit(overall_success)
                
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def _progress_callback(self, model_type: str, progress: int, message: str):
        """进度回调"""
        if not self.should_cancel:
            self.progress_updated.emit(model_type, progress, message)
    
    def cancel(self):
        """取消下载"""
        self.should_cancel = True


class ModelDownloadDialog(QDialog):
    """模型下载对话框"""
    
    def __init__(self, parent=None, models_to_download: list = None, base_dir: Optional[str] = None):
        super().__init__(parent)
        self.models_to_download = models_to_download or ['asr', 'punc']
        self.base_dir = base_dir
        self.download_worker = None
        self.is_downloading = False
        self.download_results = {}
        
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("Dou-flow - 模型下载")
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("首次运行 - 下载AI模型")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 说明文本
        info_label = QLabel(
            "Dou-flow需要下载AI模型才能正常工作。\n"
            "这是一次性操作，模型将保存在本地供后续使用。"
        )
        info_label.setWordWrap(True)
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(info_label)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(line)
        
        # 下载信息区域
        self.info_layout = QVBoxLayout()
        layout.addLayout(self.info_layout)
        
        # 为每个模型创建进度显示
        self.model_widgets = {}
        for model_type in self.models_to_download:
            model_widget = self.create_model_widget(model_type)
            self.model_widgets[model_type] = model_widget
            self.info_layout.addWidget(model_widget)
        
        # 总体进度
        self.overall_progress = QProgressBar()
        self.overall_progress.setVisible(False)
        layout.addWidget(self.overall_progress)
        
        # 状态标签
        self.status_label = QLabel("准备下载...")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)
        
        # 日志区域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(100)
        self.log_text.setVisible(False)
        layout.addWidget(self.log_text)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.show_log_button = QPushButton("显示详细日志")
        self.show_log_button.clicked.connect(self.toggle_log)
        button_layout.addWidget(self.show_log_button)
        
        button_layout.addStretch()
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.cancel_download)
        button_layout.addWidget(self.cancel_button)
        
        self.start_button = QPushButton("开始下载")
        self.start_button.clicked.connect(self.start_download)
        self.start_button.setDefault(True)
        button_layout.addWidget(self.start_button)
        
        layout.addLayout(button_layout)
    
    def create_model_widget(self, model_type: str) -> QFrame:
        """创建单个模型的进度显示组件"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Shape.Box)
        frame.setLineWidth(1)
        
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 模型信息
        model_info = {
            'asr': {'name': '语音识别模型', 'size': '约1GB'},
            'punc': {'name': '标点符号模型', 'size': '约300MB'}
        }
        
        info = model_info.get(model_type, {'name': f'{model_type}模型', 'size': '未知大小'})
        
        # 标题
        title_layout = QHBoxLayout()
        title_label = QLabel(info['name'])
        title_font = QFont()
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_layout.addWidget(title_label)
        
        size_label = QLabel(info['size'])
        size_label.setStyleSheet("color: gray;")
        title_layout.addWidget(size_label)
        title_layout.addStretch()
        
        layout.addLayout(title_layout)
        
        # 进度条
        progress_bar = QProgressBar()
        progress_bar.setVisible(False)
        layout.addWidget(progress_bar)
        
        # 状态标签
        status_label = QLabel("等待下载...")
        status_label.setStyleSheet("color: gray;")
        layout.addWidget(status_label)
        
        # 保存组件引用
        frame.progress_bar = progress_bar
        frame.status_label = status_label
        
        return frame
    
    def setup_connections(self):
        """设置信号连接"""
        pass
    
    def start_download(self):
        """开始下载"""
        if self.is_downloading:
            return
        
        self.is_downloading = True
        self.start_button.setEnabled(False)
        self.overall_progress.setVisible(True)
        self.status_label.setText("正在下载模型...")
        
        # 显示所有进度条
        for model_type, widget in self.model_widgets.items():
            widget.progress_bar.setVisible(True)
            widget.status_label.setText("准备下载...")
        
        # 创建并启动下载线程
        self.download_worker = ModelDownloadWorker(
            self.models_to_download, 
            self.base_dir
        )
        
        # 连接信号
        self.download_worker.progress_updated.connect(self.on_progress_updated)
        self.download_worker.download_completed.connect(self.on_download_completed)
        self.download_worker.all_completed.connect(self.on_all_completed)
        self.download_worker.error_occurred.connect(self.on_error_occurred)
        
        self.download_worker.start()
        
        self.log("开始下载AI模型...")
    
    def cancel_download(self):
        """取消下载"""
        if self.is_downloading and self.download_worker:
            reply = QMessageBox.question(
                self, 
                "确认取消", 
                "确定要取消下载吗？\n取消后需要重新下载才能使用应用。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.download_worker.cancel()
                self.status_label.setText("正在取消下载...")
                self.log("用户取消下载")
        else:
            self.reject()
    
    def on_progress_updated(self, model_type: str, progress: int, message: str):
        """处理进度更新"""
        if model_type in self.model_widgets:
            widget = self.model_widgets[model_type]
            widget.progress_bar.setValue(progress)
            widget.status_label.setText(message)
        
        # 更新总体进度
        total_progress = sum(
            widget.progress_bar.value() 
            for widget in self.model_widgets.values()
        ) / len(self.model_widgets)
        
        self.overall_progress.setValue(int(total_progress))
        
        self.log(f"[{model_type}] {message}")
    
    def on_download_completed(self, model_type: str, success: bool):
        """处理单个模型下载完成"""
        self.download_results[model_type] = success
        
        if model_type in self.model_widgets:
            widget = self.model_widgets[model_type]
            if success:
                widget.progress_bar.setValue(100)
                widget.status_label.setText("✅ 下载完成")
                widget.status_label.setStyleSheet("color: green;")
                self.log(f"[{model_type}] 下载成功")
            else:
                widget.status_label.setText("❌ 下载失败")
                widget.status_label.setStyleSheet("color: red;")
                self.log(f"[{model_type}] 下载失败")
    
    def on_all_completed(self, overall_success: bool):
        """处理所有下载完成"""
        self.is_downloading = False
        
        if overall_success:
            self.status_label.setText("✅ 所有模型下载完成！")
            self.status_label.setStyleSheet("color: green;")
            self.cancel_button.setText("关闭")
            self.log("所有模型下载完成，应用可以正常使用了")
            
            # 自动关闭对话框
            QTimer.singleShot(2000, self.accept)
        else:
            self.status_label.setText("❌ 部分模型下载失败")
            self.status_label.setStyleSheet("color: red;")
            self.cancel_button.setText("关闭")
            
            # 显示重试按钮
            self.start_button.setText("重试")
            self.start_button.setEnabled(True)
            
            self.log("部分模型下载失败，请检查网络连接后重试")
    
    def on_error_occurred(self, error_message: str):
        """处理错误"""
        self.is_downloading = False
        self.status_label.setText("❌ 下载出错")
        self.status_label.setStyleSheet("color: red;")
        self.cancel_button.setText("关闭")
        self.start_button.setText("重试")
        self.start_button.setEnabled(True)
        
        self.log(f"下载出错: {error_message}")
        
        QMessageBox.critical(
            self, 
            "下载错误", 
            f"下载过程中发生错误：\n{error_message}\n\n请检查网络连接后重试。"
        )
    
    def toggle_log(self):
        """切换日志显示"""
        if self.log_text.isVisible():
            self.log_text.setVisible(False)
            self.show_log_button.setText("显示详细日志")
            self.resize(500, 400)
        else:
            self.log_text.setVisible(True)
            self.show_log_button.setText("隐藏详细日志")
            self.resize(500, 500)
    
    def log(self, message: str):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())


def main():
    """测试函数"""
    app = QApplication(sys.argv)
    
    dialog = ModelDownloadDialog(models_to_download=['asr', 'punc'])
    result = dialog.exec()
    
    if result == QDialog.DialogCode.Accepted:
        print("下载完成")
    else:
        print("下载取消")
    
    sys.exit()


if __name__ == '__main__':
    main()
