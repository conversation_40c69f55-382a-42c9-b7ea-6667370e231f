# 历史记录项组件 - 单个历史记录的显示容器
# 包含文本内容、时间戳、布局和样式管理
# 遵循Apple Human Interface Guidelines设计规范

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QListWidgetItem, QPushButton, QApplication
from PyQt6.QtCore import Qt, QSize, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon, QPixmap, QPainter, QColor
from .text_label import TextLabel
from ..simple_style_config import style_config
from datetime import datetime

class CopyButton(QPushButton):
    """复制按钮 - Apple风格的图标按钮"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(24, 24)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self.setToolTip("复制文本")
        self._setup_styles()
    
    def _setup_styles(self):
        """设置按钮样式"""
        self.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 4px;
                padding: 2px;
            }
            QPushButton:hover {
                background-color: rgba(0, 0, 0, 0.06);
            }
            QPushButton:pressed {
                background-color: rgba(0, 0, 0, 0.1);
            }
        """)
    
    def paintEvent(self, event):
        """绘制复制图标"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 设置画笔颜色
        color = QColor(style_config.get_color('secondary_text'))
        painter.setPen(color)
        painter.setBrush(Qt.BrushStyle.NoBrush)
        
        # 绘制复制图标（两个重叠的矩形）
        rect = self.rect()
        center_x = rect.center().x()
        center_y = rect.center().y()
        
        # 后面的矩形（稍微偏移）
        painter.drawRect(center_x - 5, center_y - 5, 8, 8)
        # 前面的矩形
        painter.drawRect(center_x - 3, center_y - 3, 8, 8)

class HistoryItemWidget(QWidget):
    """历史记录项的自定义Widget - Apple设计规范
    
    每个历史记录项的容器，包含：
    - 文本内容显示（主要内容）
    - 时间戳显示（次要信息）
    - 统一的内边距和间距
    - Apple风格的视觉层次
    """
    
    def __init__(self, text, timestamp=None, parent=None):
        super().__init__(parent)
        self.original_text = text
        self.timestamp = timestamp
        self._setup_layout()
        self._create_content_widgets(text, timestamp)
        self._setup_styles()
    
    def _setup_layout(self):
        """设置布局 - 根据 Figma 设计的水平布局"""
        # 主水平布局 - 对应 Figma 的 flex justify-between items-start
        self.main_layout = QHBoxLayout(self)
        margins = style_config.get_history_item_margins()
        self.main_layout.setContentsMargins(
            margins.get('left', 16),
            margins.get('top', 12), 
            margins.get('right', 16),
            margins.get('bottom', 12)
        )
        self.main_layout.setSpacing(style_config.get_spacing('content_spacing'))
        # 设置对齐方式为顶部对齐 (items-start)
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.setLayout(self.main_layout)
    
    def _create_content_widgets(self, text, timestamp):
        """创建内容组件 - Figma 设计: flex justify-between items-start"""
        
        # 左侧：文本内容（占据主要空间）
        self.text_label = TextLabel(text, self)
        self.main_layout.addWidget(self.text_label, 1)  # stretch factor = 1，占用剩余空间
        
        # 右侧：控制区域（时间戳 + 复制按钮，对应 flex-shrink-0）
        self._create_control_area(timestamp)
    
    def _create_control_area(self, timestamp):
        """创建右侧控制区域 - Figma 设计: flex-shrink-0"""
        # 创建控制区域容器
        control_widget = QWidget()
        control_layout = QHBoxLayout(control_widget)
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(12)  # 对应 Figma 的 mr-3 间距
        
        # 创建时间戳标签（如果有时间戳）
        if timestamp:
            self.timestamp_label = QLabel()
            formatted_time = self._format_timestamp(timestamp)
            self.timestamp_label.setText(formatted_time)
            
            # 设置时间戳样式 - 对应 Figma 的 text-[var(--text-muted)] text-xs
            timestamp_font = QFont(style_config.get_font_family())
            timestamp_font.setPixelSize(11)  # text-xs
            timestamp_font.setWeight(400)     # normal weight
            self.timestamp_label.setFont(timestamp_font)
            
            timestamp_color = style_config.get_color('timestamp_text')
            self.timestamp_label.setStyleSheet(f"color: {timestamp_color};")
            self.timestamp_label.setAlignment(Qt.AlignmentFlag.AlignTop)
            
            control_layout.addWidget(self.timestamp_label)
        
        # 创建复制按钮
        self.copy_button = CopyButton(self)
        self.copy_button.clicked.connect(self._copy_text)
        control_layout.addWidget(self.copy_button)
        
        # 将控制区域添加到主布局（stretch factor = 0，不拉伸）
        self.main_layout.addWidget(control_widget, 0, Qt.AlignmentFlag.AlignTop)
    
    def _copy_text(self):
        """复制文本到剪贴板"""
        try:
            # 获取纯文本内容（去除HTML标签）
            import re
            plain_text = re.sub(r'<[^>]+>', '', self.original_text)
            
            # 复制到剪贴板
            clipboard = QApplication.clipboard()
            clipboard.setText(plain_text)
            
            # 可选：显示复制成功的视觉反馈
            self.copy_button.setToolTip("已复制!")
            QTimer.singleShot(2000, lambda: self.copy_button.setToolTip("复制文本"))
            
        except Exception as e:
            import logging
            logging.error(f"复制文本失败: {e}")
    
    
    def _format_timestamp(self, timestamp):
        """格式化时间戳显示 - 修复: 使用固定参考时间而不是实时当前时间"""
        if not timestamp:
            return ""
        
        try:
            if isinstance(timestamp, str):
                # 尝试解析字符串时间戳
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            elif isinstance(timestamp, datetime):
                dt = timestamp
            else:
                return str(timestamp)
            
            # 关键修复：使用创建时的参考时间，而不是实时的当前时间
            # 避免历史记录在界面刷新时被错误地重新格式化
            if not hasattr(self, '_reference_time'):
                self._reference_time = datetime.now()
            now = self._reference_time
            
            # 如果是今天，只显示时间
            if dt.date() == now.date():
                return dt.strftime("%H:%M")
            # 如果是昨天
            elif (now - dt).days == 1:
                return f"昨天 {dt.strftime('%H:%M')}"
            # 如果是本年，显示月日和时间
            elif dt.year == now.year:
                return dt.strftime("%m月%d日 %H:%M")
            # 其他情况显示完整日期
            else:
                return dt.strftime("%Y年%m月%d日 %H:%M")
                
        except Exception as e:
            import logging
            logging.error(f"时间戳格式化失败: {e}, timestamp: {timestamp}")
            return str(timestamp)
    
    def _setup_styles(self):
        """设置样式 - 真正的macOS设计规范"""
        card_background = style_config.get_color('card_background')
        hover_color = style_config.get_color('hover')
        separator_color = style_config.get_color('separator')
        corner_radius = style_config.get_layout('corner_radius')
        shadow_color = style_config.get_color('card_shadow')
        
        self.setStyleSheet(f"""
            HistoryItemWidget {{
                background-color: {card_background};
                border-radius: {corner_radius}px;
                border: 1px solid {separator_color};
                margin: 2px 0px;
            }}
            HistoryItemWidget:hover {{
                background-color: {hover_color};
                border-color: {style_config.get_color('border')};
            }}
        """)
    
    def refresh_styles(self):
        """刷新样式和布局"""
        # 重新设置边距
        margins = style_config.get_history_item_margins()
        self.main_layout.setContentsMargins(
            margins.get('left', 16),
            margins.get('top', 12), 
            margins.get('right', 16),
            margins.get('bottom', 12)
        )
        self.main_layout.setSpacing(style_config.get_spacing('content_spacing'))
        
        # 重新设置样式
        self._setup_styles()
        
        # 刷新文本标签样式
        if hasattr(self, 'text_label'):
            self.text_label.refresh_styles()
        
        # 刷新时间戳标签样式
        if hasattr(self, 'timestamp_label'):
            timestamp_font = QFont(style_config.get_font_family())
            timestamp_font.setPixelSize(style_config.get_timestamp_font_size())
            timestamp_font.setWeight(int(style_config.get_timestamp_font_weight()))
            self.timestamp_label.setFont(timestamp_font)
            
            timestamp_color = style_config.get_color('timestamp_text')
            self.timestamp_label.setStyleSheet(f"""
                QLabel {{
                    color: {timestamp_color};
                    margin-top: {style_config.get_spacing('timestamp_spacing')}px;
                }}
            """)
        
        # 更新几何信息
        self.updateGeometry()
    
    def getText(self):
        """获取原始HTML文本内容"""
        return self.original_text
    
    def getTimestamp(self):
        """获取时间戳"""
        return self.timestamp
    
    def setText(self, text):
        """设置文本内容并更新显示"""
        self.original_text = text
        self.text_label.setText(text)
        self.text_label.updateGeometry()
        self.updateGeometry()
    
    def setTimestamp(self, timestamp):
        """设置时间戳并更新显示"""
        self.timestamp = timestamp
        if hasattr(self, 'timestamp_label'):
            formatted_time = self._format_timestamp(timestamp)
            self.timestamp_label.setText(formatted_time)
        elif timestamp:
            # 如果之前没有时间戳，现在需要重新创建控制区域
            # 这种情况下应该重新构建整个widget
            pass  # 暂时跳过，因为这种情况很少发生
        self.updateGeometry()
    
    def sizeHint(self):
        """返回widget的建议大小 - 适配水平布局"""
        margins = self.main_layout.contentsMargins()
        
        # 获取父容器可用宽度
        total_width = self._get_actual_available_width()
        
        # 计算控制区域宽度（时间戳 + 复制按钮 + 间距）
        control_area_width = 0
        if hasattr(self, 'timestamp_label'):
            control_area_width += 40  # 时间戳大约宽度
        control_area_width += 24     # 复制按钮宽度
        control_area_width += 12     # 间距
        
        # 计算文本可用宽度
        content_spacing = self.main_layout.spacing()
        available_text_width = (total_width - margins.left() - margins.right() 
                               - control_area_width - content_spacing)
        
        # 计算文本高度
        text_height = 50  # 默认高度
        if hasattr(self, 'text_label'):
            text_font_metrics = self.text_label.fontMetrics()
            plain_text = self.text_label._get_plain_text()
            
            # 使用水平布局的文本度量计算
            text_rect = text_font_metrics.boundingRect(
                0, 0, available_text_width, 10000,
                Qt.TextFlag.TextWordWrap | Qt.TextFlag.TextWrapAnywhere,
                plain_text
            )
            
            line_height = text_font_metrics.height()
            line_spacing_multiplier = style_config.get_spacing('line_spacing_multiplier')
            line_spacing = int(line_height * line_spacing_multiplier)
            
            if text_rect.height() > 0:
                estimated_lines = max(1, (text_rect.height() + line_height - 1) // line_height)
            else:
                estimated_lines = 1
                
            # 处理显式换行符  
            if '\n' in plain_text:
                actual_lines = plain_text.count('\n') + 1
                estimated_lines = max(estimated_lines, actual_lines)
            
            text_height = estimated_lines * line_spacing + 8  # 减少内边距
        
        # 控制区域高度（时间戳和复制按钮的最大高度）
        control_height = 24  # 复制按钮高度
        
        # 总高度 = 边距 + max(文本高度, 控制区域高度)
        content_height = max(text_height, control_height)
        total_height = margins.top() + margins.bottom() + content_height
        
        # 应用最小高度
        min_height = style_config.get_layout('min_item_height')
        final_height = max(total_height, min_height)
        
        return QSize(total_width, final_height)
    
    def _get_actual_available_width(self):
        """获取实际可用宽度 - 简化版本"""
        try:
            # 获取父容器宽度
            parent_widget = self.parent()
            while parent_widget:
                if hasattr(parent_widget, 'viewport'):
                    # 这是QListWidget，获取viewport宽度
                    viewport_width = parent_widget.viewport().width()
                    if viewport_width > 50:  # 确保宽度合理
                        return viewport_width - 20  # 减去边距
                elif 'List' in parent_widget.__class__.__name__ and hasattr(parent_widget, 'width'):
                    # 其他列表容器
                    container_width = parent_widget.width()
                    if container_width > 50:
                        return container_width - 20
                parent_widget = parent_widget.parent()
            
            # 兜底：使用固定宽度
            return 380
            
        except Exception as e:
            import logging
            logging.error(f"获取实际可用宽度失败: {e}")
            return 380

class HistoryItem(QListWidgetItem):
    """历史记录列表项数据模型
    
    用于存储历史记录的数据，包含：
    - 文本内容
    - 时间戳信息
    """
    
    def __init__(self, text, timestamp=None):
        super().__init__()
        self.text = text
        self.timestamp = timestamp
        self.setSizeHint(QSize(0, 0))  # 初始大小，会被自动调整
    
    def getText(self):
        """获取文本内容"""
        return self.text
    
    def getTimestamp(self):
        """获取时间戳"""
        return self.timestamp