#!/usr/bin/env python3
"""
智能模型下载管理器
支持首次使用时自动下载模型，带进度显示和错误处理
"""

import os
import sys
import logging
import subprocess
import threading
import time
from pathlib import Path
from typing import Optional, Callable, Dict, Any
import requests
from tqdm import tqdm

class ModelDownloader:
    """智能模型下载管理器"""
    
    # 模型配置
    MODELS = {
        'asr': {
            'name': 'speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch',
            'url': 'https://www.modelscope.cn/iic/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch.git',
            'size_mb': 1024,  # 约1GB
            'required': True,
            'description': '语音识别模型'
        },
        'punc': {
            'name': 'punc_ct-transformer_zh-cn-common-vocab272727-pytorch',
            'url': 'https://www.modelscope.cn/iic/punc_ct-transformer_zh-cn-common-vocab272727-pytorch.git',
            'size_mb': 300,   # 约300MB
            'required': False,
            'description': '标点符号模型'
        }
    }
    
    def __init__(self, base_dir: Optional[str] = None, progress_callback: Optional[Callable] = None):
        """
        初始化模型下载器
        
        Args:
            base_dir: 模型存储基础目录
            progress_callback: 进度回调函数 callback(model_type, progress, message)
        """
        if base_dir is None:
            # 获取应用程序的基础路径
            if getattr(sys, 'frozen', False):
                base_dir = sys._MEIPASS
            else:
                base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        self.base_dir = Path(base_dir)
        self.models_dir = self.base_dir / "src" / "modelscope" / "hub" / "damo"
        self.progress_callback = progress_callback
        
        # 创建模型目录
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
    
    def check_model_exists(self, model_type: str) -> bool:
        """检查模型是否已存在"""
        if model_type not in self.MODELS:
            return False
        
        model_info = self.MODELS[model_type]
        model_path = self.models_dir / model_info['name']
        
        # 检查目录是否存在且包含必要文件
        if not model_path.exists():
            return False
        
        # 检查关键文件
        key_files = ['model.pt', 'config.yaml']
        for key_file in key_files:
            if not (model_path / key_file).exists():
                self.logger.warning(f"模型 {model_type} 缺少关键文件: {key_file}")
                return False
        
        return True
    
    def get_missing_models(self) -> Dict[str, Dict[str, Any]]:
        """获取缺失的模型列表"""
        missing = {}
        for model_type, model_info in self.MODELS.items():
            if not self.check_model_exists(model_type):
                missing[model_type] = model_info
        return missing
    
    def check_network_connection(self) -> bool:
        """检查网络连接"""
        try:
            response = requests.get('https://www.modelscope.cn', timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def check_git_available(self) -> bool:
        """检查git是否可用"""
        try:
            subprocess.run(['git', '--version'], 
                         capture_output=True, check=True, timeout=5)
            return True
        except:
            return False
    
    def download_model(self, model_type: str, force: bool = False) -> bool:
        """
        下载指定模型
        
        Args:
            model_type: 模型类型
            force: 是否强制重新下载
            
        Returns:
            bool: 下载是否成功
        """
        if model_type not in self.MODELS:
            self.logger.error(f"未知的模型类型: {model_type}")
            return False
        
        model_info = self.MODELS[model_type]
        model_path = self.models_dir / model_info['name']
        
        # 检查是否已存在
        if not force and self.check_model_exists(model_type):
            self.logger.info(f"模型 {model_type} 已存在，跳过下载")
            if self.progress_callback:
                self.progress_callback(model_type, 100, f"{model_info['description']}已存在")
            return True
        
        # 检查网络连接
        if not self.check_network_connection():
            self.logger.error("网络连接失败，无法下载模型")
            if self.progress_callback:
                self.progress_callback(model_type, 0, "网络连接失败")
            return False
        
        # 检查git
        if not self.check_git_available():
            self.logger.error("Git不可用，无法下载模型")
            if self.progress_callback:
                self.progress_callback(model_type, 0, "Git不可用")
            return False
        
        try:
            # 删除旧的模型目录（如果存在）
            if model_path.exists():
                import shutil
                shutil.rmtree(model_path)
            
            # 开始下载
            self.logger.info(f"开始下载 {model_info['description']}...")
            if self.progress_callback:
                self.progress_callback(model_type, 0, f"开始下载{model_info['description']}...")
            
            # 使用git clone下载
            cmd = [
                'git', 'clone', 
                '--depth', '1',  # 只下载最新版本
                '--single-branch',  # 只下载主分支
                model_info['url'], 
                str(model_path)
            ]
            
            # 启动下载进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 启动进度监控
            progress_thread = threading.Thread(
                target=self._monitor_download_progress,
                args=(model_path, model_type, model_info)
            )
            progress_thread.daemon = True
            progress_thread.start()
            
            # 等待下载完成
            stdout, stderr = process.communicate()
            
            if process.returncode != 0:
                self.logger.error(f"下载失败: {stderr}")
                if self.progress_callback:
                    self.progress_callback(model_type, 0, f"下载失败: {stderr}")
                return False
            
            # 验证下载结果
            if self.check_model_exists(model_type):
                self.logger.info(f"{model_info['description']}下载完成")
                if self.progress_callback:
                    self.progress_callback(model_type, 100, f"{model_info['description']}下载完成")
                return True
            else:
                self.logger.error(f"{model_info['description']}下载后验证失败")
                if self.progress_callback:
                    self.progress_callback(model_type, 0, "下载后验证失败")
                return False
                
        except Exception as e:
            self.logger.error(f"下载 {model_type} 时发生错误: {e}")
            if self.progress_callback:
                self.progress_callback(model_type, 0, f"下载错误: {str(e)}")
            return False
    
    def _monitor_download_progress(self, model_path: Path, model_type: str, model_info: Dict):
        """监控下载进度"""
        expected_size = model_info['size_mb'] * 1024 * 1024  # 转换为字节
        
        while not model_path.exists():
            time.sleep(1)
        
        last_size = 0
        stall_count = 0
        
        while True:
            try:
                # 计算当前目录大小
                current_size = sum(f.stat().st_size for f in model_path.rglob('*') if f.is_file())
                
                # 计算进度百分比
                progress = min(int((current_size / expected_size) * 100), 99)
                
                # 检查是否停滞
                if current_size == last_size:
                    stall_count += 1
                    if stall_count > 30:  # 30秒无变化认为完成或失败
                        break
                else:
                    stall_count = 0
                    last_size = current_size
                
                # 更新进度
                if self.progress_callback:
                    size_mb = current_size / (1024 * 1024)
                    self.progress_callback(
                        model_type, 
                        progress, 
                        f"下载中... {size_mb:.1f}MB / {model_info['size_mb']}MB"
                    )
                
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"监控下载进度时出错: {e}")
                break
    
    def download_all_models(self, required_only: bool = False) -> Dict[str, bool]:
        """
        下载所有模型
        
        Args:
            required_only: 是否只下载必需的模型
            
        Returns:
            Dict[str, bool]: 各模型下载结果
        """
        results = {}
        
        for model_type, model_info in self.MODELS.items():
            if required_only and not model_info['required']:
                continue
            
            results[model_type] = self.download_model(model_type)
        
        return results
    
    def get_total_download_size(self) -> int:
        """获取需要下载的总大小（MB）"""
        missing_models = self.get_missing_models()
        return sum(info['size_mb'] for info in missing_models.values())
    
    def cleanup_incomplete_downloads(self):
        """清理不完整的下载"""
        for model_type in self.MODELS:
            if not self.check_model_exists(model_type):
                model_path = self.models_dir / self.MODELS[model_type]['name']
                if model_path.exists():
                    self.logger.info(f"清理不完整的模型: {model_type}")
                    import shutil
                    shutil.rmtree(model_path)


def main():
    """命令行测试"""
    import argparse
    
    parser = argparse.ArgumentParser(description='模型下载器')
    parser.add_argument('--check', action='store_true', help='检查模型状态')
    parser.add_argument('--download', choices=['asr', 'punc', 'all'], help='下载指定模型')
    parser.add_argument('--cleanup', action='store_true', help='清理不完整的下载')
    
    args = parser.parse_args()
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    def progress_callback(model_type, progress, message):
        print(f"[{model_type}] {progress}% - {message}")
    
    downloader = ModelDownloader(progress_callback=progress_callback)
    
    if args.check:
        missing = downloader.get_missing_models()
        if missing:
            print("缺失的模型:")
            for model_type, info in missing.items():
                print(f"  - {model_type}: {info['description']} ({info['size_mb']}MB)")
        else:
            print("所有模型都已存在")
    
    elif args.download:
        if args.download == 'all':
            results = downloader.download_all_models()
            for model_type, success in results.items():
                status = "成功" if success else "失败"
                print(f"{model_type}: {status}")
        else:
            success = downloader.download_model(args.download)
            print(f"下载结果: {'成功' if success else '失败'}")
    
    elif args.cleanup:
        downloader.cleanup_incomplete_downloads()
        print("清理完成")


if __name__ == '__main__':
    main()
