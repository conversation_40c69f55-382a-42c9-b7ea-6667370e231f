#!/usr/bin/env python3
"""
Dou-flow 自动更新机制
支持应用和模型的独立更新
"""

import os
import sys
import json
import requests
import subprocess
import logging
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Optional, Tuple, List
from packaging import version
import hashlib
import time

class UpdateChecker:
    """更新检查器"""
    
    def __init__(self, current_version: str = "1.0.0"):
        self.current_version = current_version
        self.github_repo = "ttmouse/Wispr-Flow-CN"
        self.update_server = "https://api.github.com"
        self.logger = logging.getLogger(__name__)
        
        # 更新配置
        self.update_config = {
            'check_interval_hours': 24,  # 检查更新间隔
            'auto_download': False,      # 是否自动下载
            'auto_install': False,       # 是否自动安装
            'include_prereleases': False # 是否包含预发布版本
        }
        
        self.last_check_file = Path.home() / ".dou-flow-update-check"
    
    def should_check_update(self) -> bool:
        """检查是否应该检查更新"""
        if not self.last_check_file.exists():
            return True
        
        try:
            with open(self.last_check_file, 'r') as f:
                data = json.load(f)
                last_check = data.get('last_check', 0)
                interval_seconds = self.update_config['check_interval_hours'] * 3600
                
                return time.time() - last_check > interval_seconds
        except Exception:
            return True
    
    def update_last_check(self):
        """更新最后检查时间"""
        try:
            data = {'last_check': time.time()}
            with open(self.last_check_file, 'w') as f:
                json.dump(data, f)
        except Exception as e:
            self.logger.warning(f"无法更新检查时间: {e}")
    
    def get_latest_release(self) -> Optional[Dict]:
        """获取最新发布版本"""
        try:
            url = f"{self.update_server}/repos/{self.github_repo}/releases/latest"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            return response.json()
        except Exception as e:
            self.logger.error(f"获取最新版本失败: {e}")
            return None
    
    def check_for_updates(self) -> Tuple[bool, Optional[Dict]]:
        """检查是否有更新"""
        if not self.should_check_update():
            return False, None
        
        self.logger.info("检查应用更新...")
        
        latest_release = self.get_latest_release()
        if not latest_release:
            return False, None
        
        latest_version = latest_release.get('tag_name', '').lstrip('v')
        
        try:
            if version.parse(latest_version) > version.parse(self.current_version):
                self.logger.info(f"发现新版本: {latest_version}")
                self.update_last_check()
                return True, latest_release
            else:
                self.logger.info("当前已是最新版本")
                self.update_last_check()
                return False, None
        except Exception as e:
            self.logger.error(f"版本比较失败: {e}")
            return False, None
    
    def download_update(self, release_info: Dict, download_dir: Optional[str] = None) -> Optional[str]:
        """下载更新文件"""
        if not download_dir:
            download_dir = tempfile.mkdtemp(prefix="dou-flow-update-")
        
        # 查找DMG文件
        dmg_asset = None
        for asset in release_info.get('assets', []):
            if asset['name'].endswith('.dmg'):
                dmg_asset = asset
                break
        
        if not dmg_asset:
            self.logger.error("未找到DMG安装包")
            return None
        
        download_url = dmg_asset['browser_download_url']
        filename = dmg_asset['name']
        file_path = os.path.join(download_dir, filename)
        
        self.logger.info(f"下载更新文件: {filename}")
        
        try:
            response = requests.get(download_url, stream=True, timeout=30)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        if total_size > 0:
                            progress = (downloaded / total_size) * 100
                            self.logger.info(f"下载进度: {progress:.1f}%")
            
            self.logger.info(f"下载完成: {file_path}")
            return file_path
            
        except Exception as e:
            self.logger.error(f"下载失败: {e}")
            return None
    
    def verify_download(self, file_path: str, expected_hash: Optional[str] = None) -> bool:
        """验证下载文件"""
        if not os.path.exists(file_path):
            return False
        
        # 基本文件检查
        if os.path.getsize(file_path) < 1024 * 1024:  # 至少1MB
            self.logger.error("下载文件太小，可能损坏")
            return False
        
        # 如果提供了哈希值，进行验证
        if expected_hash:
            self.logger.info("验证文件完整性...")
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            
            if sha256_hash.hexdigest() != expected_hash:
                self.logger.error("文件哈希验证失败")
                return False
        
        return True
    
    def install_update(self, dmg_path: str) -> bool:
        """安装更新"""
        try:
            self.logger.info("开始安装更新...")
            
            # 挂载DMG
            mount_result = subprocess.run(
                ['hdiutil', 'attach', dmg_path, '-nobrowse'],
                capture_output=True, text=True
            )
            
            if mount_result.returncode != 0:
                self.logger.error(f"挂载DMG失败: {mount_result.stderr}")
                return False
            
            # 解析挂载点
            mount_point = None
            for line in mount_result.stdout.split('\n'):
                if '/Volumes/' in line:
                    mount_point = line.split('\t')[-1].strip()
                    break
            
            if not mount_point:
                self.logger.error("无法找到挂载点")
                return False
            
            # 查找应用文件
            app_path = None
            for item in os.listdir(mount_point):
                if item.endswith('.app'):
                    app_path = os.path.join(mount_point, item)
                    break
            
            if not app_path:
                self.logger.error("DMG中未找到应用文件")
                subprocess.run(['hdiutil', 'detach', mount_point], capture_output=True)
                return False
            
            # 复制应用到Applications
            dest_path = "/Applications/Dou-flow.app"
            
            # 备份现有应用
            if os.path.exists(dest_path):
                backup_path = f"{dest_path}.backup.{int(time.time())}"
                self.logger.info(f"备份现有应用到: {backup_path}")
                shutil.move(dest_path, backup_path)
            
            # 复制新应用
            self.logger.info("安装新版本...")
            shutil.copytree(app_path, dest_path)
            
            # 卸载DMG
            subprocess.run(['hdiutil', 'detach', mount_point], capture_output=True)
            
            self.logger.info("更新安装完成")
            return True
            
        except Exception as e:
            self.logger.error(f"安装更新失败: {e}")
            return False


class ModelUpdater:
    """模型更新器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.model_versions_url = "https://raw.githubusercontent.com/ttmouse/Wispr-Flow-CN/main/model_versions.json"
    
    def get_current_model_versions(self) -> Dict[str, str]:
        """获取当前模型版本"""
        # 这里应该从本地文件或配置中读取当前模型版本
        # 暂时返回默认版本
        return {
            'asr': 'v2.0.4',
            'punc': 'v2.0.4'
        }
    
    def get_latest_model_versions(self) -> Optional[Dict[str, str]]:
        """获取最新模型版本"""
        try:
            response = requests.get(self.model_versions_url, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            self.logger.error(f"获取模型版本信息失败: {e}")
            return None
    
    def check_model_updates(self) -> List[str]:
        """检查模型更新"""
        current_versions = self.get_current_model_versions()
        latest_versions = self.get_latest_model_versions()
        
        if not latest_versions:
            return []
        
        updates_available = []
        for model_type, current_ver in current_versions.items():
            latest_ver = latest_versions.get(model_type)
            if latest_ver and latest_ver != current_ver:
                updates_available.append(model_type)
        
        return updates_available
    
    def update_models(self, models_to_update: List[str]) -> bool:
        """更新指定模型"""
        try:
            from .model_downloader import ModelDownloader
            
            downloader = ModelDownloader()
            
            for model_type in models_to_update:
                self.logger.info(f"更新模型: {model_type}")
                success = downloader.download_model(model_type, force=True)
                if not success:
                    self.logger.error(f"模型 {model_type} 更新失败")
                    return False
            
            return True
        except Exception as e:
            self.logger.error(f"模型更新失败: {e}")
            return False


class UpdateManager:
    """更新管理器"""
    
    def __init__(self, current_version: str = "1.0.0"):
        self.app_updater = UpdateChecker(current_version)
        self.model_updater = ModelUpdater()
        self.logger = logging.getLogger(__name__)
    
    def check_all_updates(self) -> Dict[str, any]:
        """检查所有更新"""
        result = {
            'app_update_available': False,
            'app_release_info': None,
            'model_updates_available': [],
            'timestamp': time.time()
        }
        
        # 检查应用更新
        app_update, release_info = self.app_updater.check_for_updates()
        result['app_update_available'] = app_update
        result['app_release_info'] = release_info
        
        # 检查模型更新
        model_updates = self.model_updater.check_model_updates()
        result['model_updates_available'] = model_updates
        
        return result
    
    def perform_updates(self, update_info: Dict, auto_install: bool = False) -> bool:
        """执行更新"""
        success = True
        
        # 更新模型
        if update_info['model_updates_available']:
            self.logger.info("开始更新模型...")
            model_success = self.model_updater.update_models(
                update_info['model_updates_available']
            )
            if not model_success:
                success = False
        
        # 更新应用
        if update_info['app_update_available'] and auto_install:
            self.logger.info("开始更新应用...")
            release_info = update_info['app_release_info']
            
            # 下载更新
            dmg_path = self.app_updater.download_update(release_info)
            if dmg_path and self.app_updater.verify_download(dmg_path):
                app_success = self.app_updater.install_update(dmg_path)
                if not app_success:
                    success = False
            else:
                success = False
        
        return success


def main():
    """测试函数"""
    logging.basicConfig(level=logging.INFO)
    
    manager = UpdateManager()
    updates = manager.check_all_updates()
    
    print("更新检查结果:")
    print(f"应用更新: {updates['app_update_available']}")
    print(f"模型更新: {updates['model_updates_available']}")
    
    if updates['app_update_available']:
        release_info = updates['app_release_info']
        print(f"最新版本: {release_info['tag_name']}")
        print(f"发布说明: {release_info['body'][:100]}...")


if __name__ == '__main__':
    main()
