#!/usr/bin/env python3
"""
Dou-flow 性能监控器
监控应用性能、资源使用情况和用户体验指标
"""

import os
import sys
import time
import psutil
import threading
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import statistics

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: float
    cpu_percent: float
    memory_mb: float
    memory_percent: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float
    gpu_usage: Optional[float] = None
    temperature: Optional[float] = None

@dataclass
class ASRPerformanceMetrics:
    """ASR性能指标"""
    timestamp: float
    audio_duration_ms: float
    processing_time_ms: float
    model_load_time_ms: float
    transcription_accuracy: Optional[float] = None
    confidence_score: Optional[float] = None

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, log_file: Optional[str] = None):
        self.is_monitoring = False
        self.monitor_thread = None
        self.metrics_history: List[PerformanceMetrics] = []
        self.asr_metrics_history: List[ASRPerformanceMetrics] = []
        
        # 配置
        self.config = {
            'sample_interval': 1.0,  # 采样间隔（秒）
            'max_history_size': 3600,  # 最大历史记录数
            'alert_cpu_threshold': 80.0,  # CPU使用率警告阈值
            'alert_memory_threshold': 80.0,  # 内存使用率警告阈值
            'log_performance_data': True,  # 是否记录性能数据
        }
        
        # 日志设置
        self.log_file = log_file or Path.home() / "dou-flow-performance.log"
        self.logger = self._setup_logging()
        
        # 进程信息
        self.process = psutil.Process()
        self.start_time = time.time()
        
        # 基线指标
        self.baseline_metrics = None
        self._collect_baseline()
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('performance_monitor')
        logger.setLevel(logging.INFO)
        
        # 文件处理器
        file_handler = logging.FileHandler(self.log_file)
        file_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        return logger
    
    def _collect_baseline(self):
        """收集基线性能指标"""
        try:
            baseline = {
                'cpu_count': psutil.cpu_count(),
                'memory_total_gb': psutil.virtual_memory().total / (1024**3),
                'disk_total_gb': psutil.disk_usage('/').total / (1024**3),
                'system_boot_time': psutil.boot_time(),
                'python_version': sys.version,
                'platform': sys.platform
            }
            self.baseline_metrics = baseline
            self.logger.info(f"基线指标收集完成: {baseline}")
        except Exception as e:
            self.logger.error(f"收集基线指标失败: {e}")
    
    def start_monitoring(self):
        """开始性能监控"""
        if self.is_monitoring:
            self.logger.warning("性能监控已在运行")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        
        self.logger.info("性能监控已停止")
        self._save_performance_report()
    
    def _monitor_loop(self):
        """监控循环"""
        last_disk_io = self.process.io_counters()
        last_network = psutil.net_io_counters()
        
        while self.is_monitoring:
            try:
                # 收集系统指标
                cpu_percent = self.process.cpu_percent()
                memory_info = self.process.memory_info()
                memory_mb = memory_info.rss / (1024**2)
                memory_percent = self.process.memory_percent()
                
                # 磁盘IO
                current_disk_io = self.process.io_counters()
                disk_read_mb = (current_disk_io.read_bytes - last_disk_io.read_bytes) / (1024**2)
                disk_write_mb = (current_disk_io.write_bytes - last_disk_io.write_bytes) / (1024**2)
                last_disk_io = current_disk_io
                
                # 网络IO
                current_network = psutil.net_io_counters()
                network_sent_mb = (current_network.bytes_sent - last_network.bytes_sent) / (1024**2)
                network_recv_mb = (current_network.bytes_recv - last_network.bytes_recv) / (1024**2)
                last_network = current_network
                
                # GPU使用率（如果可用）
                gpu_usage = self._get_gpu_usage()
                
                # 温度（如果可用）
                temperature = self._get_temperature()
                
                # 创建性能指标
                metrics = PerformanceMetrics(
                    timestamp=time.time(),
                    cpu_percent=cpu_percent,
                    memory_mb=memory_mb,
                    memory_percent=memory_percent,
                    disk_io_read_mb=disk_read_mb,
                    disk_io_write_mb=disk_write_mb,
                    network_sent_mb=network_sent_mb,
                    network_recv_mb=network_recv_mb,
                    gpu_usage=gpu_usage,
                    temperature=temperature
                )
                
                # 添加到历史记录
                self.metrics_history.append(metrics)
                
                # 限制历史记录大小
                if len(self.metrics_history) > self.config['max_history_size']:
                    self.metrics_history.pop(0)
                
                # 检查警告阈值
                self._check_alerts(metrics)
                
                # 记录性能数据
                if self.config['log_performance_data']:
                    self._log_metrics(metrics)
                
            except Exception as e:
                self.logger.error(f"性能监控出错: {e}")
            
            time.sleep(self.config['sample_interval'])
    
    def _get_gpu_usage(self) -> Optional[float]:
        """获取GPU使用率"""
        try:
            # 尝试使用nvidia-ml-py获取GPU信息
            import pynvml
            pynvml.nvmlInit()
            handle = pynvml.nvmlDeviceGetHandleByIndex(0)
            utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
            return float(utilization.gpu)
        except:
            return None
    
    def _get_temperature(self) -> Optional[float]:
        """获取系统温度"""
        try:
            temps = psutil.sensors_temperatures()
            if temps:
                # 获取CPU温度
                for name, entries in temps.items():
                    if 'cpu' in name.lower() or 'core' in name.lower():
                        if entries:
                            return entries[0].current
            return None
        except:
            return None
    
    def _check_alerts(self, metrics: PerformanceMetrics):
        """检查性能警告"""
        if metrics.cpu_percent > self.config['alert_cpu_threshold']:
            self.logger.warning(f"CPU使用率过高: {metrics.cpu_percent:.1f}%")
        
        if metrics.memory_percent > self.config['alert_memory_threshold']:
            self.logger.warning(f"内存使用率过高: {metrics.memory_percent:.1f}%")
    
    def _log_metrics(self, metrics: PerformanceMetrics):
        """记录性能指标"""
        log_data = {
            'type': 'performance',
            'timestamp': metrics.timestamp,
            'cpu_percent': metrics.cpu_percent,
            'memory_mb': metrics.memory_mb,
            'memory_percent': metrics.memory_percent
        }
        self.logger.info(json.dumps(log_data))
    
    def record_asr_performance(self, audio_duration_ms: float, processing_time_ms: float, 
                              model_load_time_ms: float = 0, confidence_score: float = None):
        """记录ASR性能指标"""
        metrics = ASRPerformanceMetrics(
            timestamp=time.time(),
            audio_duration_ms=audio_duration_ms,
            processing_time_ms=processing_time_ms,
            model_load_time_ms=model_load_time_ms,
            confidence_score=confidence_score
        )
        
        self.asr_metrics_history.append(metrics)
        
        # 限制历史记录大小
        if len(self.asr_metrics_history) > self.config['max_history_size']:
            self.asr_metrics_history.pop(0)
        
        # 计算实时倍数
        if audio_duration_ms > 0:
            realtime_factor = processing_time_ms / audio_duration_ms
            self.logger.info(f"ASR性能: 音频{audio_duration_ms:.0f}ms, 处理{processing_time_ms:.0f}ms, 实时倍数{realtime_factor:.2f}x")
    
    def get_performance_summary(self, duration_minutes: int = 60) -> Dict[str, Any]:
        """获取性能摘要"""
        cutoff_time = time.time() - (duration_minutes * 60)
        
        # 过滤最近的指标
        recent_metrics = [m for m in self.metrics_history if m.timestamp > cutoff_time]
        recent_asr_metrics = [m for m in self.asr_metrics_history if m.timestamp > cutoff_time]
        
        if not recent_metrics:
            return {'error': '没有足够的性能数据'}
        
        # 计算统计信息
        cpu_values = [m.cpu_percent for m in recent_metrics]
        memory_values = [m.memory_mb for m in recent_metrics]
        
        summary = {
            'duration_minutes': duration_minutes,
            'sample_count': len(recent_metrics),
            'cpu_stats': {
                'avg': statistics.mean(cpu_values),
                'max': max(cpu_values),
                'min': min(cpu_values),
                'std': statistics.stdev(cpu_values) if len(cpu_values) > 1 else 0
            },
            'memory_stats': {
                'avg_mb': statistics.mean(memory_values),
                'max_mb': max(memory_values),
                'min_mb': min(memory_values),
                'std_mb': statistics.stdev(memory_values) if len(memory_values) > 1 else 0
            },
            'asr_stats': {}
        }
        
        # ASR性能统计
        if recent_asr_metrics:
            processing_times = [m.processing_time_ms for m in recent_asr_metrics]
            audio_durations = [m.audio_duration_ms for m in recent_asr_metrics]
            realtime_factors = [p/a for p, a in zip(processing_times, audio_durations) if a > 0]
            
            summary['asr_stats'] = {
                'transcription_count': len(recent_asr_metrics),
                'avg_processing_time_ms': statistics.mean(processing_times),
                'avg_realtime_factor': statistics.mean(realtime_factors) if realtime_factors else 0,
                'max_realtime_factor': max(realtime_factors) if realtime_factors else 0
            }
        
        return summary
    
    def _save_performance_report(self):
        """保存性能报告"""
        try:
            report = {
                'session_info': {
                    'start_time': self.start_time,
                    'end_time': time.time(),
                    'duration_seconds': time.time() - self.start_time
                },
                'baseline_metrics': self.baseline_metrics,
                'performance_summary': self.get_performance_summary(duration_minutes=60),
                'total_metrics_collected': len(self.metrics_history),
                'total_asr_metrics_collected': len(self.asr_metrics_history)
            }
            
            report_file = Path.home() / f"dou-flow-performance-report-{int(time.time())}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"性能报告已保存: {report_file}")
            
        except Exception as e:
            self.logger.error(f"保存性能报告失败: {e}")
    
    def optimize_performance(self) -> List[str]:
        """性能优化建议"""
        suggestions = []
        
        if not self.metrics_history:
            return ["没有足够的性能数据进行分析"]
        
        # 分析最近的性能数据
        recent_metrics = self.metrics_history[-100:]  # 最近100个样本
        
        avg_cpu = statistics.mean([m.cpu_percent for m in recent_metrics])
        avg_memory = statistics.mean([m.memory_mb for m in recent_metrics])
        
        # CPU优化建议
        if avg_cpu > 70:
            suggestions.append("CPU使用率较高，建议关闭其他不必要的应用程序")
        
        # 内存优化建议
        if avg_memory > 1000:  # 1GB
            suggestions.append("内存使用量较大，建议重启应用释放内存")
        
        # ASR性能建议
        if self.asr_metrics_history:
            recent_asr = self.asr_metrics_history[-10:]  # 最近10次转录
            avg_realtime = statistics.mean([
                m.processing_time_ms / m.audio_duration_ms 
                for m in recent_asr 
                if m.audio_duration_ms > 0
            ])
            
            if avg_realtime > 2.0:
                suggestions.append("语音识别处理较慢，建议检查模型加载状态或系统资源")
        
        if not suggestions:
            suggestions.append("当前性能表现良好，无需特别优化")
        
        return suggestions


# 全局性能监控实例
_global_monitor: Optional[PerformanceMonitor] = None

def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控实例"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = PerformanceMonitor()
    return _global_monitor

def start_performance_monitoring():
    """启动性能监控"""
    monitor = get_performance_monitor()
    monitor.start_monitoring()

def stop_performance_monitoring():
    """停止性能监控"""
    monitor = get_performance_monitor()
    monitor.stop_monitoring()

def record_asr_performance(audio_duration_ms: float, processing_time_ms: float, **kwargs):
    """记录ASR性能"""
    monitor = get_performance_monitor()
    monitor.record_asr_performance(audio_duration_ms, processing_time_ms, **kwargs)


def main():
    """测试函数"""
    monitor = PerformanceMonitor()
    
    print("启动性能监控...")
    monitor.start_monitoring()
    
    # 模拟一些ASR操作
    for i in range(5):
        time.sleep(2)
        monitor.record_asr_performance(
            audio_duration_ms=1000 + i * 100,
            processing_time_ms=500 + i * 50,
            confidence_score=0.9 + i * 0.01
        )
    
    print("获取性能摘要...")
    summary = monitor.get_performance_summary(duration_minutes=1)
    print(json.dumps(summary, indent=2))
    
    print("获取优化建议...")
    suggestions = monitor.optimize_performance()
    for suggestion in suggestions:
        print(f"- {suggestion}")
    
    print("停止性能监控...")
    monitor.stop_monitoring()


if __name__ == '__main__':
    main()
