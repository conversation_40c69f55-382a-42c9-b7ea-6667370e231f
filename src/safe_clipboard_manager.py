"""
安全的剪贴板管理器 - 专为打包环境设计
避免pynput等可能导致问题的依赖
"""

import logging
import time

class SafeClipboardManager:
    """安全的剪贴板管理器 - 为打包环境设计"""
    
    def __init__(self):
        self.logger = logging.getLogger('SafeClipboardManager')
        self.logger.info("安全剪贴板管理器已初始化（打包模式）")
    
    def copy_to_clipboard(self, text):
        """复制文本到剪贴板 - 安全版本"""
        try:
            # 使用基础的pyperclip功能
            import pyperclip
            clean_text = text.strip() if text else ""
            pyperclip.copy(clean_text)
            self.logger.info("文本已复制到剪贴板")
            return True
        except Exception as e:
            self.logger.error(f"复制到剪贴板失败: {e}")
            return False
    
    def safe_copy_and_paste(self, text):
        """安全的复制粘贴方法 - 使用AppleScript实现粘贴"""
        try:
            # 首先复制到剪贴板
            if not self.copy_to_clipboard(text):
                return False
            
            # 使用AppleScript执行粘贴操作
            return self._safe_paste_with_applescript()
        except Exception as e:
            self.logger.error(f"安全复制粘贴失败: {e}")
            return False
    
    def _safe_paste_with_applescript(self):
        """使用AppleScript安全地执行粘贴操作"""
        try:
            import subprocess
            import time
            
            # 短暂延迟确保剪贴板内容已设置
            time.sleep(0.1)
            
            # 使用AppleScript执行Command+V粘贴
            script = '''
            tell application "System Events"
                keystroke "v" using command down
            end tell
            '''
            
            result = subprocess.run(['osascript', '-e', script], 
                                  capture_output=True, text=True, timeout=2)
            
            if result.returncode == 0:
                self.logger.info("AppleScript粘贴执行成功")
                return True
            else:
                self.logger.error(f"AppleScript粘贴失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"AppleScript粘贴执行失败: {e}")
            return False
    
    def paste_text_with_delay(self, text, delay_ms=0):
        """带延迟的粘贴操作 - 使用AppleScript实现粘贴"""
        try:
            if delay_ms > 0:
                time.sleep(delay_ms / 1000.0)
            return self.safe_copy_and_paste(text)
        except Exception as e:
            self.logger.error(f"延迟粘贴失败: {e}")
            return False