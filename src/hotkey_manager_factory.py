import logging
import os
import sys
from typing import Optional
from .hotkey_manager_base import HotkeyManagerBase
from .hotkey_manager import PythonHotkeyManager
from .hammerspoon_hotkey_manager import HammerspoonHotkeyManager
from .safe_hotkey_manager import SafeHotkeyManager

class HotkeyManagerFactory:
    """热键管理器工厂类"""
    
    @staticmethod
    def _is_packaged_app() -> bool:
        """检测是否运行在打包的应用中"""
        # 更精确的检测：必须同时满足环境变量和启动条件
        has_env_flag = os.environ.get('DISABLE_INPUT_SOURCE_CHECK') == '1'
        launched_from_app = os.environ.get('LAUNCHED_FROM_APP_BUNDLE') == '1'
        
        # 检查是否在.app包中运行
        in_app_bundle = (
            (sys.executable and '.app/Contents/' in sys.executable) or
            ('.app' in os.getcwd())
        )
        
        # 只有同时满足环境变量和启动条件才认为是打包环境
        return has_env_flag and (launched_from_app or in_app_bundle)
    
    @staticmethod
    def create_hotkey_manager(scheme: str, settings_manager=None) -> Optional[HotkeyManagerBase]:
        """创建热键管理器实例
        
        Args:
            scheme: 热键方案，'hammerspoon' 或 'python'
            settings_manager: 设置管理器实例
            
        Returns:
            HotkeyManagerBase: 热键管理器实例，创建失败时返回None
        """
        logger = logging.getLogger('HotkeyManagerFactory')
        
        # 检查是否在打包环境中运行
        if HotkeyManagerFactory._is_packaged_app():
            logger.info("检测到打包环境，使用安全热键管理器")
            try:
                return SafeHotkeyManager(settings_manager)
            except Exception as e:
                logger.error(f"创建安全热键管理器失败: {e}")
                return None
        
        try:
            if scheme == 'hammerspoon':
                # 检查Hammerspoon是否可用
                manager = HammerspoonHotkeyManager(settings_manager)
                if manager._check_hammerspoon_available():
                    logger.info("创建Hammerspoon热键管理器成功")
                    return manager
                else:
                    logger.warning("Hammerspoon不可用，回退到Python方案")
                    # 自动回退到Python方案
                    if settings_manager:
                        settings_manager.set_hotkey_scheme('python')
                    return PythonHotkeyManager(settings_manager)
                    
            elif scheme == 'python':
                logger.info("创建Python热键管理器成功")
                return PythonHotkeyManager(settings_manager)
                
            else:
                logger.error(f"未知的热键方案: {scheme}")
                # 默认使用Python方案
                logger.info("使用默认的Python热键管理器")
                return PythonHotkeyManager(settings_manager)
                
        except Exception as e:
            logger.error(f"创建热键管理器失败: {e}")
            # 出错时回退到Python方案
            try:
                logger.info("回退到Python热键管理器")
                return PythonHotkeyManager(settings_manager)
            except Exception as fallback_error:
                logger.error(f"回退方案也失败: {fallback_error}")
                return None
    
    @staticmethod
    def get_available_schemes() -> list:
        """获取可用的热键方案列表
        
        Returns:
            list: 可用方案列表
        """
        schemes = ['python']  # Python方案总是可用
        
        # 检查Hammerspoon是否可用
        try:
            import subprocess
            result = subprocess.run(['which', 'hs'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                schemes.insert(0, 'hammerspoon')  # Hammerspoon优先
        except Exception:
            pass
        
        return schemes
    
    @staticmethod
    def is_scheme_available(scheme: str) -> bool:
        """检查指定方案是否可用
        
        Args:
            scheme: 方案名称
            
        Returns:
            bool: 方案是否可用
        """
        if scheme == 'python':
            return True
        elif scheme == 'hammerspoon':
            try:
                import subprocess
                result = subprocess.run(['which', 'hs'], capture_output=True, text=True, timeout=5)
                return result.returncode == 0
            except Exception:
                return False
        else:
            return False
    
    @staticmethod
    def get_scheme_info(scheme: str) -> dict:
        """获取方案信息
        
        Args:
            scheme: 方案名称
            
        Returns:
            dict: 方案信息字典
        """
        scheme_info = {
            'hammerspoon': {
                'name': 'Hammerspoon方案',
                'description': '基于Hammerspoon的热键监听，更稳定可靠',
                'pros': ['系统级集成', '高稳定性', '低CPU占用', '事件驱动'],
                'cons': ['需要安装Hammerspoon', '依赖外部工具'],
                'requirements': ['需要安装Hammerspoon应用'],
                'recommended': True
            },
            'python': {
                'name': 'Python原生方案',
                'description': '基于pynput和Quartz的Python实现',
                'pros': ['无需外部依赖', '完全控制', '快速开发'],
                'cons': ['稳定性一般', 'CPU占用较高', '权限敏感'],
                'requirements': ['macOS辅助功能权限'],
                'recommended': False
            }
        }
        
        return scheme_info.get(scheme, {
            'name': '未知方案',
            'description': '未知的热键方案',
            'pros': [],
            'cons': [],
            'requirements': [],
            'recommended': False
        })