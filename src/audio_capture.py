import sounddevice as sd
import numpy as np
import time
import collections
import queue
import logging

class AudioCapture:
    def __init__(self):
        # 使用deque限制缓冲区大小，避免内存累积
        self.frames = collections.deque(maxlen=1000)  # 限制最大帧数
        self.stream = None
        self.device_index = None
        self.read_count = 0
        # 音量相关参数
        self.volume_threshold = 0.001  # 降低默认阈值提高敏感度
        self.min_valid_frames = 1      # 进一步降低最少有效帧数要求，避免过滤有效音频
        self.valid_frame_count = 0     # 有效音频帧计数
        self.max_silence_frames = 50   # 增加最大静音帧数到约2秒
        self.silence_frame_count = 0   # 连续静音帧计数
        self.debug_frame_count = 0     # 调试帧计数
        
        # sounddevice 相关
        self.recording = False
        self.audio_queue = queue.Queue()
        self.sample_rate = 16000
        self.channels = 1
        self.dtype = np.float32
        
        # 初始化音频系统
        self._initialize_audio()
        
    def _initialize_audio(self):
        """初始化音频系统"""
        try:
            # 获取可用的输入设备
            devices = sd.query_devices()
            input_devices = [i for i, device in enumerate(devices) if device['max_input_channels'] > 0]
            
            if not input_devices:
                print("未找到可用的输入设备")
                return
            
            # 优先选择真正的麦克风设备，而不是虚拟音频设备
            mic_devices = []
            for i in input_devices:
                device_info = sd.query_devices(i)
                device_name = device_info['name'].lower()
                # 优先选择包含"麦克风"或"microphone"的设备，排除虚拟设备
                if ('麦克风' in device_name or 'microphone' in device_name) and 'blackhole' not in device_name:
                    mic_devices.append(i)
            
            if mic_devices:
                self.device_index = mic_devices[0]  # 使用第一个真正的麦克风
            else:
                # 如果没找到麦克风，使用默认设备
                try:
                    self.device_index = sd.default.device[0]  # 默认输入设备
                except:
                    self.device_index = input_devices[0]  # 第一个可用输入设备
            
            # 测试设备是否工作
            device_info = sd.query_devices(self.device_index)
            print(f"使用音频设备: {device_info['name']}")
            
        except Exception as e:
            logging.error(f"初始化音频系统失败: {e}")
            self.device_index = None

    def _audio_callback(self, indata, frames, time, status):
        """音频回调函数"""
        if status:
            print(f'Audio callback status: {status}')
        if self.recording:
            try:
                # 将音频数据放入队列
                self.audio_queue.put(indata.copy(), block=False)
            except queue.Full:
                # 如果队列满了，跳过这一帧（避免阻塞）
                pass

    def start_recording(self):
        """开始录音"""
        retry_count = 0
        max_retries = 3
        
        while retry_count < max_retries:
            try:
                # 确保之前的录音已经停止
                self.stop_recording()
                
                if self.device_index is None:
                    self._initialize_audio()
                
                if self.device_index is None:
                    raise Exception("音频系统未正确初始化")
                
                self.frames.clear()
                self.read_count = 0
                self.valid_frame_count = 0
                self.silence_frame_count = 0
                self.debug_frame_count = 0
                
                # 清空队列
                while not self.audio_queue.empty():
                    try:
                        self.audio_queue.get_nowait()
                    except queue.Empty:
                        break
                
                # 开始录音
                self.recording = True
                self.stream = sd.InputStream(
                    device=self.device_index,
                    channels=self.channels,
                    samplerate=self.sample_rate,
                    dtype=self.dtype,
                    callback=self._audio_callback,
                    blocksize=1024  # 增加缓冲区大小减少数据丢失
                )
                self.stream.start()
                
                print(f"开始录音，使用设备: {sd.query_devices(self.device_index)['name']}")
                return
                
            except Exception as e:
                retry_count += 1
                logging.error(f"尝试 {retry_count}/{max_retries} 启动录音失败: {e}")
                self._cleanup()
                time.sleep(0.5)  # 等待系统资源释放
        
        raise Exception(f"在 {max_retries} 次尝试后仍无法启动录音")

    def stop_recording(self):
        """停止录音"""
        self.recording = False
        
        if self.stream is not None:
            try:
                self.stream.stop()
                self.stream.close()
            except Exception as e:
                logging.warning(f"停止录音流时出错: {e}")
            finally:
                self.stream = None
        
        # 收集队列中剩余的所有音频数据
        remaining_frames = []
        while not self.audio_queue.empty():
            try:
                frame = self.audio_queue.get_nowait()
                frame_data = frame.flatten()
                remaining_frames.append(frame_data)
            except queue.Empty:
                break
        
        # 合并所有帧：已存储的帧 + 队列中剩余的帧
        all_frames = list(self.frames) + remaining_frames
        
        print(f"🎯 音频收集完成: 存储帧={len(self.frames)}, 队列剩余帧={len(remaining_frames)}, 总帧数={len(all_frames)}")
        
        if all_frames:
            audio_data = np.concatenate(all_frames)
            print(f"🔊 最终音频数据: 长度={len(audio_data)}, 持续时间={len(audio_data)/self.sample_rate:.2f}秒")
            return audio_data.astype(np.float32)
        else:
            print("⚠️ 未收集到音频数据")
            return np.array([], dtype=np.float32)

    def read_audio(self):
        """读取音频数据"""
        if not self.recording:
            return None
            
        try:
            # 从队列中获取音频数据
            frame = self.audio_queue.get(timeout=0.1)
            frame_data = frame.flatten()
            
            # 计算音量
            volume = np.sqrt(np.mean(frame_data**2))
            
            self.debug_frame_count += 1
            
            # 始终将音频数据添加到帧列表中（避免数据丢失）
            self.frames.append(frame_data)
            
            # 检查是否为有效音频（超过阈值）
            if volume > self.volume_threshold:
                self.valid_frame_count += 1
                self.silence_frame_count = 0
            else:
                self.silence_frame_count += 1
                
            # 返回音频数据（所有帧都保留）
            return frame_data.tobytes()
                
        except queue.Empty:
            # 队列为空，返回空数据
            return b''
        except Exception as e:
            logging.error(f"读取音频数据失败: {e}")
            return None

    def _cleanup(self):
        """清理资源"""
        self.recording = False
        
        if self.stream is not None:
            try:
                self.stream.stop()
                self.stream.close()  
            except Exception as e:
                logging.warning(f"清理音频流时出错: {e}")
            finally:
                self.stream = None
        
        # 清空队列
        while not self.audio_queue.empty():
            try:
                self.audio_queue.get_nowait()
            except queue.Empty:
                break

    def cleanup(self):
        """公共清理方法，供外部调用"""
        try:
            self._cleanup()
        except Exception as e:
            logging.error(f"清理音频捕获资源失败: {e}")
    
    def __del__(self):
        """析构函数"""
        self._cleanup()

    def get_audio_data(self):
        """获取录音数据"""
        if not self.frames:
            return np.array([], dtype=np.float32)
            
        # 直接合并所有帧数据
        all_frames = list(self.frames)
        if all_frames:
            audio_data = np.concatenate(all_frames)
            return audio_data.astype(np.float32)
        else:
            return np.array([], dtype=np.float32)

    def set_volume_threshold(self, threshold):
        """设置音量阈值（0-1000的值会被转换为0-0.02的浮点数）"""
        self.volume_threshold = (threshold / 1000.0) * 0.02

    def clear_recording_data(self):
        """清理录音数据"""
        self.frames.clear()
        self.read_count = 0
        self.valid_frame_count = 0
        self.silence_frame_count = 0
        self.debug_frame_count = 0
        
    def clear_buffer(self):
        """手动清理缓冲区"""
        self.frames.clear()

    def set_device(self, device_name=None):
        """设置音频输入设备"""
        try:
            # 停止当前录音
            self.stop_recording()
            
            if device_name is None or device_name == "系统默认":
                try:
                    self.device_index = sd.default.device[0]
                except:
                    self.device_index = 0
                return True
                
            # 查找指定设备
            devices = sd.query_devices()
            for i, device in enumerate(devices):
                if (device['max_input_channels'] > 0 and 
                    device['name'] == device_name):
                    self.device_index = i
                    return True
                    
            return False
            
        except Exception as e:
            logging.error(f"设置音频设备失败: {e}")
            return False