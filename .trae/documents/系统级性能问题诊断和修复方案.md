# 系统级性能问题诊断和修复方案

## 问题描述

启动应用后，运行一段时间后出现系统级窗口拖动卡顿问题，影响所有应用的窗口操作性能。

## 根本原因分析

### 1. 热键管理器性能问题

**位置**: `src/hotkey_manager.py`

**问题**:

* **频繁的系统调用**: `_monitor_fn_key()` 方法以50Hz频率调用 `Quartz.CGEventSourceFlagsState()`

* **过度线程创建**: `_schedule_delayed_check()` 和 `_start_delayed_hotkey_check()` 创建大量短生命周期线程

* **锁竞争**: 多个线程频繁争夺 `_state_lock`

* **系统级事件监听**: 持续监听系统级键盘事件，消耗系统资源

**影响**: 高频率的系统调用和线程创建导致系统内核负载增加，影响窗口管理器性能。

### 2. 音频管理器资源消耗

**位置**: `src/audio_manager.py`

**问题**:

* **大量AppleScript执行**: 每次录音开始/结束都执行复杂的AppleScript脚本

* **多应用交互**: 同时与Music、Spotify、Chrome、Safari等多个应用交互

* **系统事件注入**: 使用 `key code 16 using {command down}` 注入系统级按键事件

**影响**: AppleScript执行和系统事件注入可能干扰系统的窗口管理和事件处理。

### 3. 事件过滤器阻塞

**位置**: `src/main.py:267-285`

**问题**:

* **主线程阻塞**: `eventFilter()` 在主线程中处理应用级事件

* **跨线程调用**: 事件处理中可能触发跨线程的窗口操作

**影响**: 主线程阻塞会直接影响UI响应性和窗口拖动性能。

## 修复方案

### 方案1: 优化热键管理器（高优先级）

#### 1.1 降低系统调用频率

```python
# 将检查频率从50Hz降低到10Hz
time.sleep(0.1)  # 从0.02改为0.1
```

#### 1.2 减少线程创建

```python
# 使用线程池替代频繁创建线程
from concurrent.futures import ThreadPoolExecutor

class PythonHotkeyManager(HotkeyManagerBase):
    def __init__(self, settings_manager=None):
        # ...
        self._thread_pool = ThreadPoolExecutor(max_workers=2, thread_name_prefix="hotkey")
        
    def _schedule_delayed_check(self, key_str):
        """使用线程池替代创建新线程"""
        def delayed_check():
            time.sleep(0.1)  # 增加到100ms
            with self._state_lock:
                self.other_keys_pressed.discard(key_str)
        
        self._thread_pool.submit(delayed_check)
```

#### 1.3 优化状态检查逻辑

```python
# 减少不必要的状态检查
def _monitor_fn_key(self):
    last_fn_state = False
    check_interval = 0.1  # 降低到10Hz
    last_check_time = 0
    
    while not self.should_stop:
        current_time = time.time()
        
        # 只在必要时检查状态
        if current_time - last_check_time >= check_interval:
            # 执行状态检查
            last_check_time = current_time
        
        time.sleep(0.05)  # 基础休眠
```

### 方案2: 优化音频管理器（中优先级）

#### 2.1 简化AppleScript脚本

```python
def _pause_all_audio(self):
    """简化的音频暂停脚本"""
    # 只处理最常用的应用，减少系统交互
    script = '''
    try
        tell application "Music" to pause
    end try
    try
        tell application "Spotify" to pause
    end try
    '''
```

#### 2.2 异步执行AppleScript

```python
from concurrent.futures import ThreadPoolExecutor

class AudioManager(QObject):
    def __init__(self, parent=None):
        super().__init__(parent)
        self._script_executor = ThreadPoolExecutor(max_workers=1)
        
    def mute_other_apps(self):
        """异步执行音频暂停"""
        self._script_executor.submit(self._pause_all_audio)
```

### 方案3: 优化事件处理（中优先级）

#### 3.1 简化事件过滤器

```python
def eventFilter(self, obj, event):
    """简化的事件过滤器"""
    try:
        if obj == self.app and event.type() == 121:
            # 使用信号异步处理，避免阻塞主线程
            self.show_window_signal.emit()
            return False
        return False
    except:
        return False
```

### 方案4: 系统资源监控（低优先级）

#### 4.1 添加性能监控

```python
import psutil
import threading

class PerformanceMonitor:
    def __init__(self):
        self.monitoring = False
        
    def start_monitoring(self):
        """启动性能监控"""
        def monitor():
            while self.monitoring:
                cpu_percent = psutil.cpu_percent(interval=1)
                memory_info = psutil.virtual_memory()
                
                if cpu_percent > 80 or memory_info.percent > 80:
                    logging.warning(f"高资源使用: CPU {cpu_percent}%, 内存 {memory_info.percent}%")
                
                time.sleep(5)
        
        self.monitoring = True
        threading.Thread(target=monitor, daemon=True).start()
```

## 实施优先级

1. **立即实施**: 热键管理器频率优化（降低到10Hz）
2. **短期实施**: 线程池替代、事件过滤器简化
3. **中期实施**: 音频管理器异步化
4. **长期实施**: 性能监控系统

## 验证方法

1. **系统监控**: 使用Activity Monitor观察CPU和内存使用
2. **窗口性能测试**: 启动应用后持续拖动其他应用窗口测试流畅度
3. **长时间运行测试**: 运行应用2-3小时后测试系统响应性
4. **资源泄漏检测**: 监控线程数量和内存使用趋势

## 预期效果

* **CPU使用率降低**: 减少50%以上的系统调用频率

* **线程数量控制**: 限制热键相关线程数量在5个以内

* **系统响应性提升**: 消除窗口拖动卡顿问题

* **稳定性增强**: 减少资源竞争和潜在的死锁风险

