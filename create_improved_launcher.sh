#!/bin/bash

# 改进的Dou-flow启动器创建脚本
# 创建一个更稳定、用户友好的启动脚本

APP_BUNDLE="Dou-flow.app"

if [ ! -d "$APP_BUNDLE" ]; then
    echo "错误：未找到应用包 $APP_BUNDLE"
    exit 1
fi

echo "创建改进的启动脚本..."

cat > "${APP_BUNDLE}/Contents/MacOS/run.command" << 'EOL'
#!/bin/bash

# Dou-flow 改进启动脚本
# 提供更好的用户体验和错误处理

# 获取应用包路径
APP_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
RESOURCES_DIR="$APP_DIR/../Resources"
cd "$RESOURCES_DIR"

# 日志文件
STARTUP_LOG="$HOME/dou-flow-startup.log"
ERROR_LOG="$HOME/dou-flow-error.log"

# 记录启动
echo "$(date): Dou-flow 启动开始" >> "$STARTUP_LOG"

# 显示用户友好的消息
show_error() {
    echo "$(date): 错误 - $1" >> "$STARTUP_LOG"
    osascript -e "display dialog \"$1\" buttons {\"确定\"} default button \"确定\" with icon stop with title \"Dou-flow 启动错误\""
    exit 1
}

show_info() {
    echo "$(date): 信息 - $1" >> "$STARTUP_LOG"
    osascript -e "display dialog \"$1\" buttons {\"确定\"} default button \"确定\" with icon note with title \"Dou-flow\""
}

show_progress() {
    echo "$(date): 进度 - $1" >> "$STARTUP_LOG"
    osascript -e "display dialog \"$1\" buttons {} giving up after 3 with title \"Dou-flow\""
}

show_question() {
    echo "$(date): 询问 - $1" >> "$STARTUP_LOG"
    local result
    result=$(osascript -e "display dialog \"$1\" buttons {\"取消\", \"继续\"} default button \"继续\" with icon question with title \"Dou-flow\"" 2>/dev/null)
    if echo "$result" | grep -q "继续"; then
        echo "$(date): 用户选择继续" >> "$STARTUP_LOG"
        return 0
    else
        echo "$(date): 用户选择取消" >> "$STARTUP_LOG"
        return 1
    fi
}

# 预检查系统要求
precheck_system() {
    echo "$(date): 开始系统预检查" >> "$STARTUP_LOG"
    
    # 检查macOS版本
    local macos_version=$(sw_vers -productVersion)
    local major_version=$(echo "$macos_version" | cut -d. -f1)
    local minor_version=$(echo "$macos_version" | cut -d. -f2)
    
    if [ "$major_version" -lt 10 ] || ([ "$major_version" -eq 10 ] && [ "$minor_version" -lt 15 ]); then
        show_error "系统版本过低：$macos_version

Dou-flow需要macOS 10.15或更高版本。
请升级您的系统后重试。"
        return 1
    fi
    
    # 检查可用磁盘空间
    local free_space=$(df -g . | tail -1 | awk '{print $4}')
    if [ "$free_space" -lt 4 ]; then
        show_error "磁盘空间不足：剩余${free_space}GB

Dou-flow需要至少4GB的可用空间来下载和运行。
请清理磁盘空间后重试。"
        return 1
    fi
    
    echo "$(date): 系统预检查通过" >> "$STARTUP_LOG"
    return 0
}

# 检查并下载模型
check_and_download_models() {
    echo "$(date): 开始检查模型" >> "$STARTUP_LOG"
    
    local ASR_MODEL_DIR="$RESOURCES_DIR/src/modelscope/hub/damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch"
    local PUNC_MODEL_DIR="$RESOURCES_DIR/src/modelscope/hub/damo/punc_ct-transformer_zh-cn-common-vocab272727-pytorch"
    
    # 检查是否需要下载模型
    local need_download=false
    if [ ! -d "$ASR_MODEL_DIR" ] || [ ! -f "$ASR_MODEL_DIR/config.yaml" ]; then
        need_download=true
    fi
    
    if [ "$need_download" = true ]; then
        echo "$(date): 需要下载模型" >> "$STARTUP_LOG"
        
        # 询问用户是否要下载模型
        if ! show_question "欢迎使用Dou-flow！

首次运行需要下载AI模型（约1.3GB）：
• 语音识别模型：约1GB
• 标点符号模型：约300MB

这是一次性操作，模型将保存在本地。

下载需要：
• 稳定的网络连接
• 约10-15分钟时间
• 至少2GB可用空间

是否现在下载？"; then
            show_info "您选择了暂不下载模型。

要使用Dou-flow，请稍后重新启动应用并选择下载模型。

提示：建议在网络条件良好时进行下载。"
            exit 0
        fi
        
        # 检查网络连接
        show_progress "检查网络连接..."
        if ! curl -s --connect-timeout 10 --max-time 30 https://www.modelscope.cn > /dev/null 2>&1; then
            show_error "网络连接失败，无法访问模型服务器。

请检查：
• 网络连接是否正常
• 防火墙设置是否阻止访问
• 是否使用了代理服务器

建议稍后重试或在网络条件更好时使用。"
            return 1
        fi
        
        # 检查git
        if ! command -v git >/dev/null 2>&1; then
            show_error "缺少必要工具：Git

Dou-flow需要Git工具来下载模型。

解决方案：
1. 打开终端应用
2. 运行命令：xcode-select --install
3. 按照提示安装Xcode Command Line Tools
4. 安装完成后重新启动Dou-flow

这是一次性设置，完成后就不需要再安装了。"
            return 1
        fi
        
        # 创建模型目录
        mkdir -p "$(dirname "$ASR_MODEL_DIR")"
        
        # 下载ASR模型
        show_progress "正在下载语音识别模型...

进度：1/2 (约1GB)
请保持网络连接，这可能需要5-10分钟。"
        
        if ! timeout 1800 git clone --depth 1 --single-branch --quiet \
            https://www.modelscope.cn/iic/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch.git \
            "$ASR_MODEL_DIR" 2>>"$ERROR_LOG"; then
            
            # 清理失败的下载
            rm -rf "$ASR_MODEL_DIR" 2>/dev/null || true
            show_error "语音识别模型下载失败。

可能原因：
• 网络连接中断
• 下载超时（30分钟）
• 服务器暂时不可用
• 磁盘空间不足

建议：
• 检查网络连接稳定性
• 稍后重试
• 查看错误日志：$ERROR_LOG"
            return 1
        fi
        
        # 验证ASR模型下载
        if [ ! -f "$ASR_MODEL_DIR/config.yaml" ]; then
            show_error "语音识别模型下载不完整。

请重新启动应用重试下载。"
            return 1
        fi
        
        # 下载标点模型
        show_progress "正在下载标点符号模型...

进度：2/2 (约300MB)
即将完成，请稍候..."
        
        if ! timeout 600 git clone --depth 1 --single-branch --quiet \
            https://www.modelscope.cn/iic/punc_ct-transformer_zh-cn-common-vocab272727-pytorch.git \
            "$PUNC_MODEL_DIR" 2>>"$ERROR_LOG"; then
            
            # 清理失败的下载
            rm -rf "$PUNC_MODEL_DIR" 2>/dev/null || true
            show_info "标点符号模型下载失败，但不影响基本功能。

语音识别功能正常，只是转录结果可能缺少标点符号。

您可以稍后重新启动应用尝试下载标点模型。"
        fi
        
        show_info "🎉 模型下载完成！

Dou-flow已准备就绪：
• 语音识别：✅ 可用
• 标点符号：$([ -f "$PUNC_MODEL_DIR/config.yaml" ] && echo "✅ 可用" || echo "⚠️ 不可用")

应用即将启动，首次加载可能需要几秒钟。"
    else
        echo "$(date): 模型已存在，跳过下载" >> "$STARTUP_LOG"
    fi
    
    return 0
}

# 检查并设置conda环境
check_and_setup_conda() {
    echo "$(date): 开始检查conda环境" >> "$STARTUP_LOG"
    
    # 设置conda路径
    export PATH="$HOME/miniconda3/bin:$HOME/anaconda3/bin:/opt/miniconda3/bin:/opt/anaconda3/bin:$PATH"
    
    # 初始化conda环境
    local conda_initialized=false
    for conda_path in "$HOME/miniconda3" "$HOME/anaconda3" "/opt/miniconda3" "/opt/anaconda3"; do
        if [ -f "$conda_path/etc/profile.d/conda.sh" ]; then
            source "$conda_path/etc/profile.d/conda.sh" 2>/dev/null || true
            conda_initialized=true
            echo "$(date): 找到conda: $conda_path" >> "$STARTUP_LOG"
            break
        fi
    done
    
    # 检查conda是否安装
    if ! command -v conda >/dev/null 2>&1; then
        show_error "未检测到conda环境。

Dou-flow需要conda来管理Python环境。

安装步骤：
1. 访问：https://docs.conda.io/en/latest/miniconda.html
2. 下载适合您系统的Miniconda
3. 按照安装向导完成安装
4. 重新启动Dou-flow

推荐选择Miniconda（较小）而不是完整的Anaconda。"
        return 1
    fi

    # 检查funasr_env环境是否存在
    if ! conda env list 2>/dev/null | grep -q "funasr_env"; then
        echo "$(date): 需要创建conda环境" >> "$STARTUP_LOG"
        
        if ! show_question "需要创建Python运行环境。

这个过程包括：
• 创建专用conda环境（约2分钟）
• 安装Python依赖包（约5分钟）

总共约需7分钟，需要网络连接。

是否现在创建？"; then
            show_info "您选择了暂不创建环境。

Dou-flow需要专用环境才能运行，请稍后重新启动应用并选择创建环境。"
            exit 0
        fi

        show_progress "正在创建Python环境...

步骤：1/2
这可能需要2-3分钟，请耐心等待。"

        # 创建环境
        if ! conda create -n funasr_env python=3.10 -y 2>>"$ERROR_LOG"; then
            show_error "创建conda环境失败。

可能原因：
• 磁盘空间不足
• 网络连接问题
• conda配置问题

请检查系统状态后重试。
错误详情请查看：$ERROR_LOG"
            return 1
        fi

        # 激活环境并安装依赖
        show_progress "正在安装依赖包...

步骤：2/2
安装Python包，这可能需要5分钟。"
        
        if [ "$conda_initialized" = true ]; then
            eval "$(conda shell.bash hook)" 2>/dev/null || true
            conda activate funasr_env 2>/dev/null || true
        fi
        
        # 安装依赖
        if [ -f "requirements.txt" ]; then
            if ! pip install -r requirements.txt 2>>"$ERROR_LOG"; then
                show_error "安装Python依赖包失败。

可能原因：
• 网络连接问题
• 磁盘空间不足
• pip配置问题

请检查网络连接和磁盘空间后重试。
错误详情请查看：$ERROR_LOG"
                return 1
            fi
        fi

        show_info "🎉 Python环境创建完成！

环境配置：
• Python版本：3.10
• 环境名称：funasr_env
• 依赖包：已安装

Dou-flow现在可以正常运行了。"
    else
        echo "$(date): conda环境已存在" >> "$STARTUP_LOG"
    fi
    
    return 0
}

# 启动应用
start_application() {
    echo "$(date): 开始启动应用" >> "$STARTUP_LOG"
    
    # 激活环境
    export PATH="$HOME/miniconda3/bin:$HOME/anaconda3/bin:/opt/miniconda3/bin:/opt/anaconda3/bin:$PATH"
    
    # 初始化conda环境
    local conda_activated=false
    for conda_path in "$HOME/miniconda3" "$HOME/anaconda3" "/opt/miniconda3" "/opt/anaconda3"; do
        if [ -f "$conda_path/etc/profile.d/conda.sh" ]; then
            source "$conda_path/etc/profile.d/conda.sh" 2>/dev/null || true
            conda_activated=true
            break
        fi
    done
    
    # 激活funasr_env环境
    if [ "$conda_activated" = true ]; then
        eval "$(conda shell.bash hook)" 2>/dev/null || true
        if ! conda activate funasr_env 2>/dev/null; then
            show_error "无法激活Python环境。

解决方案：
1. 重新启动应用
2. 如果问题持续，删除环境后重新创建：
   打开终端运行：conda env remove -n funasr_env
   然后重新启动Dou-flow"
            return 1
        fi
    else
        show_error "无法初始化conda环境。

请确保conda已正确安装并重新启动应用。"
        return 1
    fi

    # 检查Python环境
    if ! command -v python >/dev/null 2>&1; then
        show_error "Python环境异常。

请尝试重新创建环境：
1. 打开终端运行：conda env remove -n funasr_env
2. 重新启动Dou-flow"
        return 1
    fi

    # 设置环境变量
    export DISABLE_INPUT_SOURCE_CHECK=1
    export LAUNCHED_FROM_APP_BUNDLE=1
    export PYTHONUNBUFFERED=1
    export QT_MAC_DISABLE_FOREGROUND_APPLICATION_TRANSFORM=1
    export MODELSCOPE_CACHE="$RESOURCES_DIR/src/modelscope/hub"

    echo "$(date): 启动Python应用" >> "$STARTUP_LOG"
    
    # 运行程序
    if ! python main.py 2>&1 | tee -a "$ERROR_LOG"; then
        show_error "应用启动失败。

错误日志已保存到：$ERROR_LOG

常见解决方案：
1. 重新启动应用
2. 检查系统权限设置
3. 重新创建Python环境

如果问题持续，请联系技术支持并提供错误日志。"
        return 1
    fi
}

# 主函数
main() {
    # 系统预检查
    if ! precheck_system; then
        exit 1
    fi
    
    # 检查并下载模型
    if ! check_and_download_models; then
        exit 1
    fi
    
    # 检查并设置环境
    if ! check_and_setup_conda; then
        exit 1
    fi
    
    # 启动应用
    if ! start_application; then
        exit 1
    fi
    
    echo "$(date): Dou-flow 启动成功" >> "$STARTUP_LOG"
}

# 运行主函数
main "$@"
EOL

# 设置执行权限
chmod +x "${APP_BUNDLE}/Contents/MacOS/run.command"

echo "✅ 改进的启动脚本已创建完成！"
echo ""
echo "改进内容："
echo "• 更好的错误处理和用户提示"
echo "• 系统预检查（版本、磁盘空间）"
echo "• 详细的启动日志记录"
echo "• 用户友好的对话框"
echo "• 更稳定的下载和安装流程"
echo ""
echo "现在可以测试应用启动了。"
