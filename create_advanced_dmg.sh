#!/bin/bash

# Dou-flow 高级DMG创建脚本
# 创建美观的安装界面，包含拖拽安装指导

set -e

# 配置变量
APP_NAME="Dou-flow"
APP_VERSION="1.0.0"
DMG_NAME="${APP_NAME}-${APP_VERSION}"
DMG_TEMP_DIR="dmg_temp"
APP_BUNDLE="${APP_NAME}.app"
TEMPLATE_DMG="template.dmg"

echo "🎨 开始创建高级 ${APP_NAME} DMG安装包..."

# 检查应用包是否存在
if [ ! -d "${APP_BUNDLE}" ]; then
    echo "❌ 错误：找不到 ${APP_BUNDLE}"
    echo "请先运行 ./build_dou_flow_app.sh 创建应用包"
    exit 1
fi

# 清理临时文件
echo "🧹 清理临时文件..."
rm -rf "${DMG_TEMP_DIR}"
rm -f "${DMG_NAME}.dmg"
rm -f "${TEMPLATE_DMG}"

# 创建模板DMG
echo "📏 创建模板DMG..."
APP_SIZE=$(du -sm "${APP_BUNDLE}" | cut -f1)
DMG_SIZE=$((APP_SIZE + 100))

hdiutil create -size ${DMG_SIZE}m -fs HFS+ -volname "${APP_NAME}" "${TEMPLATE_DMG}"

# 挂载模板DMG
echo "💿 挂载DMG进行定制..."
DMG_MOUNT=$(hdiutil attach -readwrite -noverify -noautoopen "${TEMPLATE_DMG}" | grep '/Volumes' | awk '{print $3}')

echo "DMG挂载点：${DMG_MOUNT}"

# 复制应用包
echo "📦 复制应用包到DMG..."
cp -R "${APP_BUNDLE}" "${DMG_MOUNT}/"

# 创建应用程序文件夹的符号链接
echo "🔗 创建应用程序文件夹链接..."
ln -s /Applications "${DMG_MOUNT}/Applications"

# 复制安装说明文件
echo "📄 创建安装说明..."
cat > "${DMG_MOUNT}/安装说明.txt" << 'EOL'
欢迎使用 Dou-flow 语音转文字应用！

安装步骤：
1. 将 Dou-flow.app 拖拽到 Applications 文件夹
2. 首次运行时会自动安装依赖环境（需要网络连接）
3. 授予麦克风和辅助功能权限
4. 使用 Fn 键进行语音录制

系统要求：
- macOS 10.10 或更高版本
- 支持 Intel 和 Apple Silicon Mac
- 需要 Miniconda 环境（首次运行会自动安装）

更多信息请查看项目主页。
EOL

# 设置Finder视图选项
echo "🎨 设置DMG外观..."

# 创建.DS_Store文件来设置图标位置和窗口外观
osascript << 'EOD'
tell application "Finder"
    tell disk "Dou-flow"
        open
        set current view of container window to icon view
        set toolbar visible of container window to false
        set statusbar visible of container window to false
        set the bounds of container window to {100, 100, 600, 400}
        
        set theViewOptions to the icon view options of container window
        set arrangement of theViewOptions to not arranged
        set icon size of theViewOptions to 128
        set background picture of theViewOptions to file ".background:background.png"
        
        -- 设置图标位置
        set position of item "Dou-flow.app" of container window to {125, 160}
        set position of item "Applications" of container window to {375, 160}
        set position of item "安装说明.txt" of container window to {125, 280}
        
        close
        open
        update without registering applications
        delay 2
    end tell
end tell
EOD

# 卸载DMG
echo "📤 卸载DMG..."
hdiutil detach "${DMG_MOUNT}"

# 转换为只读压缩DMG
echo "🗜️ 转换为最终DMG格式..."
hdiutil convert "${TEMPLATE_DMG}" -format UDZO -imagekey zlib-level=9 -o "${DMG_NAME}.dmg"

# 清理临时文件
echo "🧹 清理临时文件..."
rm -f "${TEMPLATE_DMG}"

# 获取最终文件大小
DMG_SIZE_FINAL=$(du -h "${DMG_NAME}.dmg" | cut -f1)

echo ""
echo "✅ 高级DMG创建完成！"
echo "📦 文件名：${DMG_NAME}.dmg"
echo "📏 文件大小：${DMG_SIZE_FINAL}"
echo "🎨 特色：自定义布局、安装说明、拖拽界面"
echo "🎯 支持：Intel和Apple Silicon Mac (Universal)"
echo ""
echo "🚀 DMG文件已可用于分发"
echo "💡 用户体验：双击DMG → 拖拽app到Applications → 完成安装"