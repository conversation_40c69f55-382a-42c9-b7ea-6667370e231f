# Dou-flow 修复记录 - 2025年8月4日

## 修复的问题

### 1. 历史记录文字显示不完整
**问题描述**：历史记录中的文字只显示上半部分，下半部分被裁剪。

**原因分析**：
- TextLabel 组件在计算文本高度时使用了带HTML标签的文本，导致高度计算不准确
- 内边距设置过小，导致文字被裁剪

**修复方案**：
- 修改 `src/ui/components/text_label.py` 中的 `_calculate_text_size` 方法
- 使用纯文本（去除HTML标签）进行高度计算
- 增加内边距从 12px 到 20px
- 添加 20% 的额外高度缓冲区确保文字完全显示

### 2. 语音转录信息缺失
**问题描述**：语音录制到转录过程中有明显的信息丢失。

**原因分析**：
- 音频阈值设置过低（0.0001），导致大量静音被当作有效音频收集
- 音频缓冲区太小（512），可能导致数据丢失
- 音频队列大小未限制，可能影响处理性能

**修复方案**：
1. 调整音频阈值从 0.0001 到 0.01（更合理的范围）
2. 增加音频缓冲区大小从 512 到 1024
3. 设置音频队列最大容量为 500，避免内存过度使用

## 修改的文件

1. `/src/ui/components/text_label.py`
   - 修改了文本高度计算逻辑
   - 添加了必要的导入（traceback, logging）

2. `/src/audio_capture.py`
   - 调整音频阈值设置
   - 增加缓冲区大小
   - 设置队列容量限制

## 测试建议

1. **测试历史记录显示**：
   - 录制包含长文本的语音
   - 检查历史记录中文字是否完整显示
   - 测试不同长度的文本

2. **测试语音转录质量**：
   - 录制正常语速的语音
   - 检查转录结果是否完整
   - 测试不同音量的语音输入

## 后续优化建议

1. 考虑添加音频阈值的动态调整功能
2. 在设置界面中添加音频缓冲区大小的配置选项
3. 添加音频质量监控，实时显示音频信号强度
4. 考虑实现自适应的文本高度计算算法