# FunASR-Swift 项目开发日志

## 2024-01-09 15:30:00 项目初始化

- 创建了基本的项目结构，包括以下目录：
  - Sources/App: 应用程序入口
  - Sources/Models: 数据模型
  - Sources/Views: 用户界面
  - Sources/Utils: 工具类
  - Resources: 资源文件

- 实现了核心组件：
  1. AudioEngine: 音频录制引擎
  2. TranscriptionEngine: 语音转写引擎
  3. HotkeyManager: 全局热键管理
  4. ClipboardManager: 剪贴板管理
  5. AppSettings: 应用程序设置

- 创建了基本的用户界面：
  1. MainView: 主界面，显示录音状态和转写结果
  2. SettingsView: 设置界面，用于配置模型路径和功能选项

- 添加了ASR服务器相关文件：
  1. asr_server.py: ASR服务器实现
  2. start_asr.sh: 服务器启动脚本

## 2024-01-09 16:30:00 功能优化

- 修复了项目文件结构问题：
  1. 重新生成了项目文件引用ID
  2. 修复了文件引用和构建阶段的关系
  3. 确保了正确的组织结构
  4. 添加了必要的构建设置

- 优化了音频录制功能：
  1. 移除了AVAudioSession相关代码，确保macOS兼容性
  2. 优化了音频数据的采集和处理流程
  3. 修复了音频反馈问题

- 改进了热键管理：
  1. 实现了本地和全局事件监听
  2. 优化了Option键的按下和释放检测
  3. 确保了热键功能在后台也能正常工作

- 完善了设置功能：
  1. 添加了模型路径和热词表路径的选择功能
  2. 实现了设置的持久化存储
  3. 添加了开机自启动和自动复制到剪贴板的选项

## 待实现功能

1. 历史记录功能
2. 状态栏图标和菜单
3. 自定义快捷键设置
4. 转写进度显示
5. 错误处理和用户提示优化
