# 录音时音频控制方案

## 目标
实现类似 Wispr Flow 的音频控制效果：
1. 按下录音快捷键时，其他应用的音频播放要暂停
2. 系统音量图标不显示静音状态
3. 能够播放我们自己的录音提示音

## 竞品分析（2024-01-10）
通过观察竞品 Wispr Flow 的行为：
1. 按下快捷键时，系统音量图标状态不变
2. 其他正在播放音频的应用（如音乐播放器）会暂停
3. 应用自身的提示音效可以正常播放
4. 释放快捷键后，之前暂停的应用会自动恢复播放

这表明竞品可能使用了应用级别的媒体控制，而不是系统级别的音量控制。

## 可能的实现方案

### 1. Media Remote Control API
- 实现方式：使用 macOS 的媒体远程控制 API
- 原理：
  - 通过 MediaRemote 框架发送媒体控制命令
  - 类似于按下键盘上的媒体控制键
- 优点：
  - 不影响系统音量状态
  - 可以精确控制媒体应用
  - 用户体验好
- 需要研究：
  - MediaRemote 框架的使用方法
  - 应用兼容性列表
  - 权限要求

### 2. ScriptingBridge + Apple Events
- 实现方式：使用 Apple Events 发送应用控制命令
- 原理：
  - 通过 ScriptingBridge 与其他应用通信
  - 发送暂停/播放命令
- 优点：
  - 可以精确控制支持 Apple Events 的应用
  - 更灵活的控制方式
- 需要研究：
  - ScriptingBridge 的使用方法
  - 应用支持情况
  - 性能影响

### 3. Audio Session 控制（改进版）
- 实现方式：结合 Core Audio 和 Audio Session
- 原理：
  - 使用 Core Audio 获取活跃的音频应用
  - 通过 Audio Session 控制音频路由
- 优点：
  - 更底层的控制
  - 不依赖应用支持
- 需要研究：
  - Core Audio API 的使用
  - Audio Session 权限
  - 性能开销

### 4. 混合方案
- 实现方式：结合多种方案
- 原理：
  - 优先使用 Media Remote Control
  - 对于不支持的应用，尝试 Apple Events
  - 最后使用 Audio Session 作为后备方案
- 优点：
  - 最大程度的兼容性
  - 灵活的降级策略
- 需要研究：
  - 方案切换逻辑
  - 错误处理
  - 性能优化

## 建议实施步骤
1. 先实现 Media Remote Control 方案
   - 这是最接近竞品行为的方案
   - 用户体验最好
   - 实现相对简单

2. 测试各种应用的兼容性
   - 音乐播放器（Spotify, Apple Music 等）
   - 视频应用（YouTube, bilibili 等）
   - 浏览器中的媒体播放

3. 根据测试结果决定是否需要补充其他方案
   - 如果兼容性不够，可以添加 Apple Events 支持
   - 如果还有问题，考虑使用混合方案

## 技术细节
1. Media Remote Control 实现：
```python
# 示例代码
class AudioManager:
    def mute_other_apps(self):
        # 1. 获取当前正在播放的媒体应用
        active_apps = self.get_media_playing_apps()
        
        # 2. 保存当前状态
        self.paused_apps = []
        
        # 3. 向每个应用发送暂停命令
        for app in active_apps:
            if self.send_pause_command(app):
                self.paused_apps.append(app)
    
    def resume_other_apps(self):
        # 恢复之前暂停的应用
        for app in self.paused_apps:
            self.send_play_command(app)
```

## 下一步计划
1. 实现 Media Remote Control 的概念验证
2. 测试主流应用的兼容性
3. 收集用户反馈
4. 根据反馈决定是否需要补充其他方案

## 参考资料
- [Media Remote Control Framework Documentation]()
- [ScriptingBridge Programming Guide]()
- [Core Audio Overview]()
- [Apple Events Programming Guide]() 