# 环境管理指南

## 🎯 目标

统一开发和生产环境，避免"在我的机器上能跑"的问题。

## 📋 当前环境状况

### 环境类型对比

| 环境类型 | 路径 | 用途 | 推荐度 |
|---------|------|------|--------|
| **conda funasr_env** | `/Users/<USER>/miniconda3/envs/funasr_env` | 打包app使用 | ✅ **推荐** |
| **项目venv** | `./venv/` | 直接运行使用 | ❌ 不推荐 |
| **conda base** | `/Users/<USER>/miniconda3` | 系统默认 | ❌ 不推荐 |

### 问题根源

- **打包app** 使用 `conda funasr_env` 环境
- **直接运行** 使用 `venv` 环境
- **两个环境的依赖版本可能不同**，导致行为差异

## 🛠️ 解决方案

### 1. 使用环境管理脚本

```bash
# 检查当前环境状态
./tools/env_manager.sh status

# 设置推荐的conda环境
./tools/env_manager.sh setup

# 清理不需要的venv环境
./tools/env_manager.sh cleanup

# 使用推荐环境运行应用
./tools/env_manager.sh run
```

### 2. 手动环境管理

#### 推荐方式：使用conda环境

```bash
# 激活conda环境
conda activate funasr_env

# 运行应用
python src/main.py

# 打包应用（会自动使用conda环境）
bash tools/build_app.sh
```

#### 不推荐：使用venv环境

```bash
# 这种方式可能导致环境不一致
source venv/bin/activate
python src/main.py
```

## 🔧 环境设置步骤

### 步骤1：检查conda安装

```bash
conda --version
```

如果没有安装，请安装 [Miniconda](https://docs.conda.io/en/latest/miniconda.html)

### 步骤2：创建专用环境

```bash
# 创建Python 3.10环境
conda create -n funasr_env python=3.10 -y

# 激活环境
conda activate funasr_env

# 安装依赖
pip install -r requirements.txt
```

### 步骤3：验证环境

```bash
# 检查Python版本
python --version

# 检查关键包
python -c "import torch; print('PyTorch:', torch.__version__)"
python -c "import funasr; print('FunASR:', funasr.__version__)"
```

## 📝 开发工作流程

### 日常开发

```bash
# 1. 激活环境
conda activate funasr_env

# 2. 运行应用
python src/main.py

# 或者使用环境管理脚本
./tools/env_manager.sh run
```

### 打包发布

```bash
# 打包会自动使用正确的环境
bash tools/build_app.sh
```

### 依赖管理

```bash
# 添加新依赖后，更新requirements.txt
conda activate funasr_env
pip freeze > requirements.txt

# 或者手动编辑requirements.txt，然后安装
pip install -r requirements.txt
```

## 🚨 常见问题

### Q: 为什么有多个环境？

A: 历史原因导致的，建议统一使用conda环境。

### Q: 如何知道当前使用的是哪个环境？

A: 
```bash
# 查看Python路径
which python

# 查看conda环境
echo $CONDA_DEFAULT_ENV

# 使用环境管理脚本检查
./tools/env_manager.sh status
```

### Q: 可以删除venv环境吗？

A: 可以，但建议先确保conda环境工作正常：

```bash
# 测试conda环境
conda activate funasr_env
python src/main.py

# 如果正常工作，可以删除venv
rm -rf venv
```

### Q: 如何在IDE中设置正确的环境？

A: 
- **PyCharm**: Settings → Project → Python Interpreter → 选择conda环境
- **VS Code**: Ctrl+Shift+P → Python: Select Interpreter → 选择conda环境
- **终端**: 确保运行 `conda activate funasr_env`

## 🎯 最佳实践

1. **统一环境**: 开发和生产使用同一个conda环境
2. **版本锁定**: 在requirements.txt中指定具体版本号
3. **环境隔离**: 不同项目使用不同的conda环境
4. **定期清理**: 删除不使用的环境和包
5. **文档更新**: 环境变更时及时更新文档

## 📚 相关命令速查

```bash
# conda环境管理
conda env list                    # 列出所有环境
conda activate funasr_env         # 激活环境
conda deactivate                  # 退出环境
conda env remove -n funasr_env    # 删除环境

# 包管理
pip list                          # 列出已安装包
pip freeze > requirements.txt    # 导出依赖
pip install -r requirements.txt  # 安装依赖

# 环境检查
./tools/env_manager.sh status    # 检查环境状态
./tools/env_manager.sh compare   # 对比环境差异
```
