### 项目需求概述（基于 FunASR）

1. **功能需求**
   - **语音转文字功能**：使用 FunASR 实现快速高效的语音转文字功能，按下快捷键时启动语音识别，结束后将识别结果粘贴到当前输入框中。
   - **快捷键支持**：通过指定的全局快捷键来启动和停止语音转文字功能。
   - **自动粘贴**：在识别完成后，文本会自动复制并粘贴到当前焦点的输入框中，无需额外操作。
   - **多语言支持**：FunASR 支持多种语言，特别是中文，项目应能够利用这些语言模型。
   - **实时性**：FunASR 具有高效的实时性能，确保语音转文字过程接近实时，保证用户体验流畅。
   - **后台运行**：工具可以在后台运行，不影响用户的其他操作。

2. **用户交互需求**
   - **启动和停止语音识别**：用户按下设定的全局快捷键，语音识别开始。工具提供视觉或音效反馈提醒用户正在进行语音输入。
   - **结束语音识别并粘贴文本**：用户松开快捷键，语音识别结束，并自动将识别的文本粘贴到当前的输入框中。
   - **自定义快捷键**：用户可以自定义快捷键来启动和停止语音识别。

3. **技术需求**
   - **语音识别引擎**：使用 FunASR 作为语音识别引擎，确保在中文识别和多语言支持方面具有优越性能。
   - **FunASR 特性**：充分利用 FunASR 的高效架构，确保识别速度足够快，适合实时使用。
   - **全局快捷键监听**：在 macOS 系统中，通过监听全局快捷键  option 键 
   - **跨应用粘贴功能**：通过系统 API 实现自动粘贴，将识别的文本粘贴到当前焦点的输入框中，支持跨应用操作。
   - **实时音频捕获**：使用 macOS 的音频输入设备捕捉语音，并实时传递给 FunASR 进行处理。
   - **剪贴板支持**：识别的文字自动复制到剪贴板，并自动粘贴到目标应用中。

4. **非功能需求**
   - **性能要求**：确保 FunASR 的高效架构能够快速响应，识别过程流畅，且对系统性能影响小。
   - **离线支持**：FunASR 应能够支持离线模式，确保在没有网络的情况下也能正常工作。
   - **兼容性**：支持 macOS 的最新版本，并尽量兼容较旧版本。

5. **扩展功能（可选）**
   - **用户词典**：允许用户自定义词汇表，以便更好地识别专业术语。
   - **自动标点符号**：识别过程中自动添加标点符号。
   - **语音命令**：扩展为用户提供语音命令功能，通过语音控制其他操作。

---

### 开发步骤（更新）

1. **集成 FunASR**：
   - 确定 FunASR 模型版本，并进行性能优化。
   - 开发音频捕获模块，将用户的语音实时输入传递给 FunASR 进行处理。
   - 处理识别结果，显示在前端或用于自动粘贴。

2. **全局快捷键监听**：
   - 设置全局快捷键控制语音识别的启动和停止，确保用户可以通过快捷键流畅操作。

3. **自动粘贴功能**：
   - 识别完成后，自动将文本粘贴到当前的输入框中，保证操作顺畅。

4. **用户反馈与界面**：
   - 提供视觉或音效反馈，提示用户语音识别的状态（正在识别/识别完成）。
   - 设计简单的用户界面，允许用户自定义快捷键和设置其他参数。

5. **测试与优化**：
   - 测试 FunASR 的识别准确性和响应速度，确保其在 macOS 上高效运行。
   - 优化整体性能，保证长时间语音输入时保持流畅。

---

### 快捷键逻辑需求（按住 Option 键）

1. **快捷键触发机制**
   - **启动语音识别**：按住 **Option** 键开始录制语音，并将音频传递给 FunASR 进行实时转写。
   - **结束语音识别**：释放 **Option** 键结束录制，并将识别出的文字自动粘贴到当前的输入框中。

2. **粘贴逻辑**
   - 语音识别结束后，识别的文字会自动复制到剪贴板，并粘贴到当前焦点输入框中。
   - 整个过程无需额外操作，确保用户体验与苹果系统自带的听写功能一致。

