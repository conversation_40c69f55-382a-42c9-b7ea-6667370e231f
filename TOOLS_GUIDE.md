# Dou-flow 工具指南

本指南介绍了Dou-flow项目提供的各种工具和脚本，帮助开发者和用户更好地使用和维护应用。

## 📦 打包工具

### 1. 轻量级应用构建
```bash
./build_lightweight_app.sh
```
- **功能**: 创建不包含AI模型的轻量级应用包
- **大小**: 约2.4MB
- **特点**: 模型首次使用时自动下载

### 2. 轻量级DMG打包
```bash
./build_lightweight_dmg.sh
```
- **功能**: 创建完整的DMG安装包
- **大小**: 约828KB
- **包含**: 应用、安装指南、系统要求说明

### 3. 完整版应用构建
```bash
./build_standalone_app.sh
```
- **功能**: 创建包含所有依赖的完整应用包
- **大小**: 约3.6GB（包含AI模型）
- **特点**: 无需网络连接即可使用

## 🔧 开发工具

### 开发者工具集 (`tools/dev_tools.py`)

#### 环境管理
```bash
# 检查开发环境
python tools/dev_tools.py check-env

# 设置开发环境
python tools/dev_tools.py setup-env
```

#### 代码质量
```bash
# 运行测试
python tools/dev_tools.py test

# 格式化代码
python tools/dev_tools.py format

# 代码检查
python tools/dev_tools.py lint
```

#### 构建和清理
```bash
# 构建应用（轻量级）
python tools/dev_tools.py build --type lightweight

# 构建DMG
python tools/dev_tools.py build --type dmg

# 清理构建文件
python tools/dev_tools.py clean
```

#### 开发和发布
```bash
# 运行开发服务器
python tools/dev_tools.py dev

# 生成文档
python tools/dev_tools.py docs

# 创建发布版本
python tools/dev_tools.py release 1.0.1
```

## 🔍 验证工具

### 安装验证 (`tools/verify_installation.py`)

#### 基本验证
```bash
# 自动查找应用并验证
python tools/verify_installation.py

# 指定应用路径验证
python tools/verify_installation.py --app-path /Applications/Dou-flow.app

# 静默模式
python tools/verify_installation.py --quiet

# 输出报告到文件
python tools/verify_installation.py --output verification_report.json
```

#### 验证内容
- ✅ 系统要求检查
- ✅ 应用包结构验证
- ✅ 依赖环境检查
- ✅ 网络连接测试
- ✅ 系统权限状态
- ✅ 模型下载能力

## 🗑️ 卸载工具

### 完整卸载 (`tools/uninstall_dou_flow.sh`)

#### 基本卸载
```bash
# 交互式卸载
./tools/uninstall_dou_flow.sh

# 强制卸载（无确认）
./tools/uninstall_dou_flow.sh --force

# 保留AI模型文件
./tools/uninstall_dou_flow.sh --keep-models

# 查看帮助
./tools/uninstall_dou_flow.sh --help
```

#### 卸载内容
- 🗑️ 应用程序文件
- 🗑️ Conda环境
- 🗑️ AI模型文件（可选保留）
- 🗑️ 配置文件和日志
- 🗑️ 缓存文件
- 📋 系统权限清理指导

## 🔄 更新工具

### 自动更新 (`src/updater.py`)

#### 检查更新
```python
from src.updater import UpdateManager

manager = UpdateManager(current_version="1.0.0")
updates = manager.check_all_updates()

print(f"应用更新: {updates['app_update_available']}")
print(f"模型更新: {updates['model_updates_available']}")
```

#### 执行更新
```python
# 自动安装更新
success = manager.perform_updates(updates, auto_install=True)
```

## 🎨 用户界面工具

### 模型下载对话框 (`src/ui/model_download_dialog.py`)

```python
from src.ui.model_download_dialog import ModelDownloadDialog
from PyQt6.QtWidgets import QApplication

app = QApplication([])
dialog = ModelDownloadDialog(models_to_download=['asr', 'punc'])
result = dialog.exec()
```

#### 功能特性
- 📊 实时进度显示
- ❌ 取消下载功能
- 📝 详细日志记录
- 🔄 失败重试机制

## 📋 使用场景

### 开发者工作流

1. **环境设置**
   ```bash
   python tools/dev_tools.py setup-env
   ```

2. **开发过程**
   ```bash
   # 格式化代码
   python tools/dev_tools.py format
   
   # 运行测试
   python tools/dev_tools.py test
   
   # 代码检查
   python tools/dev_tools.py lint
   ```

3. **构建发布**
   ```bash
   # 清理环境
   python tools/dev_tools.py clean
   
   # 构建DMG
   python tools/dev_tools.py build --type dmg
   
   # 创建发布
   python tools/dev_tools.py release 1.0.1
   ```

### 用户安装流程

1. **下载安装**
   - 下载 `Dou-flow-Lightweight-1.0.0.dmg`
   - 拖拽到Applications文件夹

2. **验证安装**
   ```bash
   python tools/verify_installation.py
   ```

3. **首次运行**
   - 应用自动检测环境
   - 自动下载AI模型
   - 授予必要权限

4. **卸载（如需要）**
   ```bash
   ./tools/uninstall_dou_flow.sh
   ```

## 🔧 故障排除

### 常见问题

1. **环境问题**
   ```bash
   # 检查环境状态
   python tools/dev_tools.py check-env
   
   # 重新设置环境
   python tools/dev_tools.py setup-env
   ```

2. **构建失败**
   ```bash
   # 清理后重新构建
   python tools/dev_tools.py clean
   python tools/dev_tools.py build --type lightweight
   ```

3. **安装验证失败**
   ```bash
   # 详细验证报告
   python tools/verify_installation.py --output report.json
   ```

### 日志文件

- **应用日志**: `~/dou-flow-error.log`
- **构建日志**: 终端输出
- **验证报告**: `verification_report.json`

## 📈 性能监控

### 构建时间优化
- 轻量级构建: ~30秒
- DMG打包: ~2分钟
- 完整构建: ~15分钟

### 文件大小对比
| 类型 | 大小 | 说明 |
|------|------|------|
| 轻量级DMG | 828KB | 推荐分发 |
| 轻量级App | 2.4MB | 不含模型 |
| 完整版App | 3.6GB | 含完整模型 |

## 🎯 最佳实践

1. **开发阶段**: 使用轻量级构建进行快速测试
2. **测试阶段**: 使用完整验证确保质量
3. **发布阶段**: 使用轻量级DMG减少下载负担
4. **维护阶段**: 定期运行代码检查和测试

## 📞 技术支持

如果在使用工具过程中遇到问题：

1. 查看相关日志文件
2. 运行验证工具诊断
3. 检查系统要求和权限
4. 参考故障排除指南

这套工具集为Dou-flow项目提供了完整的开发、构建、分发和维护解决方案。
