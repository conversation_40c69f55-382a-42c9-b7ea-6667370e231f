#!/usr/bin/env python3
"""
Dou-flow 完整版启动脚本
适配打包环境的模块导入
"""

import sys
import os

# 获取当前脚本所在目录（Resources目录）
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')

# 将src目录添加到Python路径
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# 将Resources目录也添加到路径（为了确保依赖导入）
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 切换工作目录到src目录
os.chdir(src_dir)

# 设置关键环境变量 - 确保应用检测到打包环境
os.environ['DISABLE_INPUT_SOURCE_CHECK'] = '1'
os.environ['LAUNCHED_FROM_APP_BUNDLE'] = '1'
os.environ['PYTHONUNBUFFERED'] = '1'
os.environ['QT_MAC_DISABLE_FOREGROUND_APPLICATION_TRANSFORM'] = '1'

# 现在可以正常导入并运行main.py
if __name__ == "__main__":
    try:
        # 直接导入并运行main模块
        import main
        
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()