#!/bin/bash

# Dou-flow 轻量级DMG打包脚本
# 创建不包含AI模型的轻量DMG安装包，模型首次使用时下载

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 配置变量
APP_NAME="Dou-flow"
APP_VERSION="1.0.0"
DMG_NAME="${APP_NAME}-Lightweight-${APP_VERSION}"
DMG_TEMP_DIR="dmg_temp"
APP_BUNDLE="${APP_NAME}.app"

print_info "=== 开始创建轻量级 ${APP_NAME} DMG安装包 ==="

# 检查依赖
print_info "检查系统依赖..."
if ! command -v git >/dev/null 2>&1; then
    print_error "需要安装 git 命令行工具"
    exit 1
fi

if ! command -v hdiutil >/dev/null 2>&1; then
    print_error "需要 hdiutil 工具（macOS自带）"
    exit 1
fi

# 清理旧文件
print_info "清理旧文件..."
rm -rf "${DMG_TEMP_DIR}"
rm -f "${DMG_NAME}.dmg"
rm -f "${DMG_NAME}-temp.dmg"

# 构建轻量级应用包
print_info "构建轻量级应用包..."
if ! bash build_lightweight_app.sh; then
    print_error "轻量级应用包构建失败"
    exit 1
fi

# 创建DMG临时目录
print_info "创建DMG临时目录..."
mkdir -p "${DMG_TEMP_DIR}"

# 复制应用包到临时目录
print_info "复制应用包..."
cp -R "${APP_BUNDLE}" "${DMG_TEMP_DIR}/"

# 创建应用程序文件夹的符号链接
print_info "创建应用程序文件夹链接..."
ln -s /Applications "${DMG_TEMP_DIR}/Applications"

# 创建安装说明文件
print_info "创建安装说明..."
cat > "${DMG_TEMP_DIR}/安装说明.txt" << 'EOL'
欢迎使用 Dou-flow 语音转文字应用！

🎯 安装步骤：
1. 将 Dou-flow.app 拖拽到 Applications 文件夹
2. 首次运行时，右键点击应用选择"打开"（绕过安全检查）
3. 按照提示授予麦克风和辅助功能权限

🚀 使用方法：
- 按住 Fn 键开始录音
- 松开 Fn 键结束录音，文本自动粘贴
- 可通过设置界面自定义快捷键

⚡ 首次运行说明：
- 需要安装 Miniconda/Anaconda（如果没有的话）
- 首次启动会自动创建专用Python环境
- AI模型会在首次使用时自动下载（约1.3GB）
- 整个过程可能需要15-20分钟，请保持网络连接

🔧 系统要求：
- macOS 10.15 或更高版本
- 至少 4GB 可用存储空间
- 稳定的网络连接（首次运行）
- Git命令行工具（通常随Xcode安装）

📞 技术支持：
如遇问题，请查看 ~/dou-flow-error.log 日志文件

版本：轻量版 1.0.0
开发者：ttmouse
EOL

# 创建快速开始指南
print_info "创建快速开始指南..."
cat > "${DMG_TEMP_DIR}/快速开始.md" << 'EOL'
# Dou-flow 快速开始指南（轻量版）

## 🎯 一键安装
1. 拖拽 `Dou-flow.app` 到 `Applications` 文件夹
2. 双击启动应用

## 🔧 首次运行准备

### 必需环境：
1. **Conda环境**：如果没有，请访问 https://docs.conda.io/en/latest/miniconda.html 下载安装
2. **Git工具**：运行 `xcode-select --install` 安装

### 自动化过程：
- ✅ 检查并下载AI模型（首次使用时）
- ✅ 创建专用Python环境
- ✅ 安装所有依赖包

## 🎤 开始使用
1. 按住 **Fn 键** 开始录音
2. 说话（支持中文）
3. 松开 **Fn 键** 结束录音
4. 文本自动粘贴到当前应用

## ⚙️ 自定义设置
- 快捷键：可改为 Ctrl、Alt 等
- 热词：添加专业术语提高识别率
- 音频：调整音量阈值和录音时长

## 🔍 故障排除
- 检查麦克风权限
- 检查辅助功能权限
- 检查网络连接
- 查看日志：`~/dou-flow-error.log`

## 💡 轻量版特点
- 应用包小（约50MB）
- 模型按需下载
- 节省存储空间
- 首次运行需要网络

享受语音转文字的便利！
EOL

# 创建系统要求说明
print_info "创建系统要求说明..."
cat > "${DMG_TEMP_DIR}/系统要求.txt" << 'EOL'
Dou-flow 系统要求

最低要求：
- macOS 10.15 (Catalina) 或更高版本
- 4GB 可用存储空间
- 稳定的网络连接（首次运行）

推荐配置：
- macOS 12.0 (Monterey) 或更高版本
- 8GB 内存
- SSD 存储
- 高速网络连接

必需软件：
- Miniconda 或 Anaconda
- Git 命令行工具
- Xcode Command Line Tools

支持的Mac：
- Intel Mac (x86_64)
- Apple Silicon Mac (M1/M2/M3)

网络要求：
- 首次运行需要下载约1.3GB的AI模型
- 后续使用完全离线

权限要求：
- 麦克风访问权限
- 辅助功能权限
- 网络访问权限（首次运行）
EOL

# 复制README文件（如果存在）
if [ -f "README.md" ]; then
    print_info "复制README文件..."
    cp "README.md" "${DMG_TEMP_DIR}/项目说明.txt"
fi

# 估算DMG大小（轻量级应用包 + 50MB缓冲）
print_info "计算DMG大小..."
APP_SIZE=$(du -sm "${APP_BUNDLE}" | cut -f1)
DMG_SIZE=$((APP_SIZE + 50))
print_info "应用包大小: ${APP_SIZE}MB，DMG大小: ${DMG_SIZE}MB"

# 创建临时DMG
print_info "创建临时DMG..."
hdiutil create -size ${DMG_SIZE}m -fs HFS+ -volname "${APP_NAME}" -srcfolder "${DMG_TEMP_DIR}" "${DMG_NAME}-temp.dmg"

# 转换为最终DMG（压缩格式）
print_info "压缩DMG文件..."
hdiutil convert "${DMG_NAME}-temp.dmg" -format UDZO -imagekey zlib-level=9 -o "${DMG_NAME}.dmg"

# 清理临时文件
print_info "清理临时文件..."
rm -rf "${DMG_TEMP_DIR}"
rm -f "${DMG_NAME}-temp.dmg"

# 获取DMG文件信息
DMG_SIZE_FINAL=$(du -h "${DMG_NAME}.dmg" | cut -f1)

print_success "=== 轻量级DMG创建完成！ ==="
echo ""
echo "📦 文件名：${DMG_NAME}.dmg"
echo "📏 文件大小：${DMG_SIZE_FINAL}"
echo "🎯 特性："
echo "   - 轻量级安装包（约50MB）"
echo "   - 智能环境检测和安装"
echo "   - 模型首次使用时下载"
echo "   - 完整的用户指南"
echo "   - 支持 Intel 和 Apple Silicon Mac"
echo ""
print_info "🚀 现在可以分发这个DMG文件给用户安装使用"
print_info "💡 用户体验：双击DMG → 拖拽app到Applications → 首次运行自动配置"
echo ""
print_warning "注意：用户首次运行需要网络连接下载AI模型（约1.3GB）"
print_success "相比完整版本，此版本大大减少了下载和存储需求！"
