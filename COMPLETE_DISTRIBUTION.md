# Dou-flow 完整版分发指南

## 📦 完整版应用包

### 应用信息
- **文件名**: `Dou-flow.app`
- **大小**: 3.6GB
- **类型**: 自包含macOS应用程序包
- **架构**: Universal Binary (Intel + Apple Silicon)
- **系统要求**: macOS 10.15+

### 包含内容
- ✅ 完整Python源代码
- ✅ 预训练AI模型（FunASR中文语音识别）
  - speech_paraformer-large (语音识别模型)
  - punc_ct-transformer (标点符号模型)
- ✅ 所有资源文件和配置
- ✅ Requirements.txt 依赖列表
- ✅ 现代化macOS界面设计
- ✅ 安全的热键和剪贴板管理

## 🚀 分发方式

### 方式一：网盘分发（推荐）
1. 将 `Dou-flow.app` 文件夹上传到网盘（百度云盘、阿里云盘等）
2. 分享下载链接给用户
3. 用户下载后直接使用

### 方式二：本地拷贝
1. 使用移动硬盘或U盘拷贝 `Dou-flow.app` 
2. 直接传递给用户

### 方式三：压缩分发
```bash
# 创建压缩包（需要足够磁盘空间）
tar -czf Dou-flow-Complete-1.0.0.tar.gz Dou-flow.app
# 或使用ZIP格式
zip -r Dou-flow-Complete-1.0.0.zip Dou-flow.app
```

## 👥 用户安装指南

### 步骤1：下载应用
- 从网盘或其他渠道下载 `Dou-flow.app`
- 如果是压缩包，先解压获得应用程序

### 步骤2：安装应用
1. 将 `Dou-flow.app` 拖拽到 `/Applications` 文件夹
2. 或者放在任何喜欢的位置

### 步骤3：首次运行设置
1. **安装conda环境**（如果尚未安装）
   - 下载Miniconda：https://docs.conda.io/en/latest/miniconda.html
   - 安装适合你系统的版本

2. **启动应用**
   - 双击 `Dou-flow.app`
   - 首次运行会自动创建Python环境（需要几分钟）

3. **授予权限**
   - **麦克风权限**：系统设置 → 隐私与安全性 → 麦克风 → 勾选Dou-flow
   - **辅助功能权限**：系统设置 → 隐私与安全性 → 辅助功能 → 添加Dou-flow

### 步骤4：开始使用
- 按住 `Fn` 键开始录音
- 松开 `Fn` 键完成录音
- 文字自动识别并粘贴到当前应用

## 🎯 技术优势

### 离线运行
- 包含完整AI模型，无需网络连接即可使用
- 语音识别在本地完成，保护隐私

### 高性能
- 使用FunASR最新中文语音识别引擎
- 针对中文优化，识别准确率高
- 支持热词定制

### 现代化界面
- 遵循Apple Human Interface Guidelines
- 自动深色模式支持
- 响应式设计

### 跨架构支持
- Intel Mac和Apple Silicon Mac通用
- 自动选择最优架构运行

## 🔧 故障排除

### 常见问题

**Q: 应用无法启动**
A: 确保已安装conda环境，并检查控制台错误信息

**Q: 录音功能不工作**
A: 检查麦克风权限是否已授予

**Q: 热键不响应**
A: 检查辅助功能权限是否已授予

**Q: 首次运行很慢**
A: 正常现象，正在自动配置Python环境和依赖

### 日志查看
- 应用日志：`~/dou-flow-error.log`
- 详细日志：应用内置日志系统

## 📊 性能要求

### 最低配置
- macOS 10.15+
- 4GB RAM
- 5GB 可用磁盘空间（应用本身3.6GB + 环境1GB+）
- 麦克风设备

### 推荐配置
- macOS 12.0+
- 8GB RAM
- Apple Silicon Mac（M1/M2/M3）或Intel Mac
- 良好的麦克风设备

## 📞 技术支持

如遇问题请检查：
1. conda环境是否正确安装
2. 系统权限是否已授予
3. 磁盘空间是否充足
4. 查看错误日志文件

---

**版本**: 1.0.0  
**构建日期**: 2025-08-05  
**包大小**: 3.6GB  
**分发类型**: 完整自包含版本