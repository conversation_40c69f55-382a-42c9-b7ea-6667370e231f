#!/bin/bash

# Dou-flow 自包含应用构建脚本
# 创建真正的独立应用包，包含所有代码和依赖项

set -e

# 配置变量
APP_NAME="Dou-flow"
APP_BUNDLE="${APP_NAME}.app"

echo "🎯 开始构建自包含的 ${APP_NAME}.app..."

# 删除旧的应用包
if [ -d "${APP_BUNDLE}" ]; then
    echo "🧹 删除旧的应用包..."
    rm -rf "${APP_BUNDLE}"
fi

# 创建应用包结构
echo "📁 创建应用包结构..."
mkdir -p "${APP_BUNDLE}/Contents/"{MacOS,Resources}

# 创建 Info.plist
echo "📄 创建 Info.plist..."
cat > "${APP_BUNDLE}/Contents/Info.plist" << EOL
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>run.command</string>
    <key>CFBundleIconFile</key>
    <string>app_icon</string>
    <key>CFBundleIdentifier</key>
    <string>com.douba.douflow</string>
    <key>CFBundleName</key>
    <string>${APP_NAME}</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    <key>LSMinimumSystemVersion</key>
    <string>10.15</string>
    <key>LSArchitecturePriority</key>
    <array>
        <string>arm64</string>
        <string>x86_64</string>
    </array>
    <key>LSRequiresNativeExecution</key>
    <false/>
    <key>NSMicrophoneUsageDescription</key>
    <string>需要使用麦克风来录制音频进行语音识别</string>
    <key>NSAppleEventsUsageDescription</key>
    <string>需要访问系统事件以支持快捷键和自动粘贴功能</string>
    <key>NSAppleEventsEnabled</key>
    <true/>
    <key>NSAccessibilityUsageDescription</key>
    <string>需要辅助功能权限来支持自动粘贴和系统集成</string>
    <key>NSHighResolutionCapable</key>
    <true/>
</dict>
</plist>
EOL

# 创建 entitlements.plist
echo "🔒 创建 entitlements.plist..."
cat > "${APP_BUNDLE}/Contents/entitlements.plist" << EOL
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>com.apple.security.automation.apple-events</key>
    <true/>
    <key>com.apple.security.device.microphone</key>
    <true/>
    <key>com.apple.security.device.audio-input</key>
    <true/>
    <key>com.apple.security.cs.allow-jit</key>
    <true/>
    <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
    <true/>
    <key>com.apple.security.cs.disable-library-validation</key>
    <true/>
    <key>com.apple.security.temporary-exception.apple-events</key>
    <array>
        <string>com.apple.systemevents</string>
        <string>com.apple.finder</string>
        <string>*</string>
    </array>
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>
</dict>
</plist>
EOL

# 复制所有源代码到应用包，保持src目录结构
echo "📦 复制源代码..."
mkdir -p "${APP_BUNDLE}/Contents/Resources/src"
cp -R src/* "${APP_BUNDLE}/Contents/Resources/src/"

# 复制专用的启动脚本
echo "📄 复制完整版启动脚本..."
cp standalone_main.py "${APP_BUNDLE}/Contents/Resources/main.py"

# 复制资源文件
echo "🎨 复制资源文件..."
if [ -d "resources" ]; then
    cp -R resources/ "${APP_BUNDLE}/Contents/Resources/"
fi

# 复制requirements.txt
echo "📋 复制依赖文件..."
if [ -f "requirements.txt" ]; then
    cp requirements.txt "${APP_BUNDLE}/Contents/Resources/"
fi

# 复制图标
echo "🎯 复制应用图标..."
if [ -f "app_icon.icns" ]; then
    cp "app_icon.icns" "${APP_BUNDLE}/Contents/Resources/"
elif [ -f "iconset.icns" ]; then
    cp "iconset.icns" "${APP_BUNDLE}/Contents/Resources/app_icon.icns"
fi

# 创建自包含的启动脚本
echo "🚀 创建自包含启动脚本..."
cat > "${APP_BUNDLE}/Contents/MacOS/run.command" << 'EOL'
#!/bin/bash

# 获取应用包路径
APP_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
RESOURCES_DIR="$APP_DIR/../Resources"
cd "$RESOURCES_DIR"

# 设置错误处理
set -e

# 显示用户友好的错误信息
show_error() {
    osascript -e "display dialog \"$1\" buttons {\"确定\"} default button \"确定\" with icon stop with title \"Dou-flow 启动错误\""
    exit 1
}

show_info() {
    osascript -e "display dialog \"$1\" buttons {\"确定\"} default button \"确定\" with icon note with title \"Dou-flow\""
}

show_progress() {
    osascript -e "display dialog \"$1\" buttons {} giving up after 3 with title \"Dou-flow\""
}

# 检查并下载模型
check_and_download_models() {
    # 检查模型是否存在
    ASR_MODEL_DIR="$RESOURCES_DIR/src/modelscope/hub/damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch"
    PUNC_MODEL_DIR="$RESOURCES_DIR/src/modelscope/hub/damo/punc_ct-transformer_zh-cn-common-vocab272727-pytorch"

    if [ ! -d "$ASR_MODEL_DIR" ] || [ ! -d "$PUNC_MODEL_DIR" ]; then
        show_info "首次运行需要下载AI模型，这可能需要10-15分钟时间，请保持网络连接..."

        # 创建模型目录
        mkdir -p "$(dirname "$ASR_MODEL_DIR")"

        # 下载ASR模型
        if [ ! -d "$ASR_MODEL_DIR" ]; then
            show_progress "正在下载语音识别模型（约1GB）..."
            if ! git clone --depth 1 https://www.modelscope.cn/iic/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch.git "$ASR_MODEL_DIR"; then
                show_error "下载语音识别模型失败，请检查网络连接"
            fi
        fi

        # 下载标点模型
        if [ ! -d "$PUNC_MODEL_DIR" ]; then
            show_progress "正在下载标点符号模型（约300MB）..."
            if ! git clone --depth 1 https://www.modelscope.cn/iic/punc_ct-transformer_zh-cn-common-vocab272727-pytorch.git "$PUNC_MODEL_DIR"; then
                show_error "下载标点符号模型失败，请检查网络连接"
            fi
        fi

        show_info "模型下载完成！应用即将启动。"
    fi
}

# 检查并安装conda环境
check_and_setup_conda() {
    # 设置conda路径
    export PATH="$HOME/miniconda3/bin:$HOME/anaconda3/bin:$PATH"

    # 初始化conda环境
    if [ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]; then
        source "$HOME/miniconda3/etc/profile.d/conda.sh"
    elif [ -f "$HOME/anaconda3/etc/profile.d/conda.sh" ]; then
        source "$HOME/anaconda3/etc/profile.d/conda.sh"
    fi

    # 检查conda是否安装
    if ! command -v conda >/dev/null 2>&1; then
        show_error "未检测到conda环境。请先安装Miniconda或Anaconda：

1. 访问 https://docs.conda.io/en/latest/miniconda.html
2. 下载适合您系统的版本
3. 安装后重新启动此应用"
        return 1
    fi

    # 检查wispr-flow-python311环境是否存在
    if ! conda env list | grep -q "wispr-flow-python311"; then
        show_info "首次运行需要创建专用环境，这可能需要几分钟时间..."

        # 创建环境
        conda create -n wispr-flow-python311 python=3.11 -y || show_error "创建conda环境失败"

        # 激活环境并安装依赖
        eval "$(conda shell.bash hook)"
        conda activate wispr-flow-python311

        # 安装基础依赖
        pip install PyQt6 PyObjC-framework-Cocoa PyObjC-framework-Quartz PyObjC-framework-AppKit || show_error "安装基础依赖失败"

        # 安装AI依赖
        pip install torch torchaudio || show_error "安装AI依赖失败"
        pip install funasr modelscope || show_error "安装语音识别依赖失败"

        # 安装其他依赖
        if [ -f "requirements.txt" ]; then
            pip install -r requirements.txt || show_error "安装项目依赖失败"
        fi

        show_info "环境创建完成！"
    fi
}

# 主要逻辑
main() {
    # 检查并下载模型
    check_and_download_models

    # 检查并设置环境
    check_and_setup_conda

    # 激活环境
    export PATH="$HOME/miniconda3/bin:$HOME/anaconda3/bin:$PATH"

    # 初始化conda环境
    if [ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]; then
        source "$HOME/miniconda3/etc/profile.d/conda.sh"
    elif [ -f "$HOME/anaconda3/etc/profile.d/conda.sh" ]; then
        source "$HOME/anaconda3/etc/profile.d/conda.sh"
    fi

    # 激活wispr-flow-python311环境
    conda activate wispr-flow-python311 || show_error "无法激活conda环境 wispr-flow-python311"

    # 检查Python环境
    if ! command -v python >/dev/null 2>&1; then
        show_error "Python环境异常，请重新安装conda环境"
    fi

    # 设置关键环境变量 - 标识这是打包环境
    export DISABLE_INPUT_SOURCE_CHECK=1
    export LAUNCHED_FROM_APP_BUNDLE=1
    export PYTHONUNBUFFERED=1
    export QT_MAC_DISABLE_FOREGROUND_APPLICATION_TRANSFORM=1
    export MODELSCOPE_CACHE="$RESOURCES_DIR/src/modelscope/hub"

    # 运行程序
    python main.py 2>&1 | tee "$HOME/dou-flow-error.log"
}

main "$@"
EOL

# 设置执行权限
echo "🔧 设置执行权限..."
chmod +x "${APP_BUNDLE}/Contents/MacOS/run.command"

# 移除扩展属性
echo "🧹 清理扩展属性..."
xattr -cr "${APP_BUNDLE}"

# 计算应用包大小
APP_SIZE=$(du -sh "${APP_BUNDLE}" | cut -f1)

# 对应用进行签名
echo "✍️ 对应用进行代码签名..."
codesign --force --deep --sign - --entitlements "${APP_BUNDLE}/Contents/entitlements.plist" "${APP_BUNDLE}" || echo "⚠️ 代码签名失败，但应用仍可使用"

echo ""
echo "✅ 自包含应用构建完成！"
echo "📦 应用位置: ${APP_BUNDLE}"
echo "📏 应用大小: ${APP_SIZE}"
echo "🎯 特性: 包含完整源代码和资源文件"
echo "🚀 现在可以双击 ${APP_BUNDLE} 启动应用"
echo ""
echo "💡 注意: 首次运行会自动创建conda环境和安装依赖"