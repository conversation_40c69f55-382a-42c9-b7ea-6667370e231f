# Dou-flow 分发包说明

## 📦 分发方案

我们提供两种分发方案以满足不同用户需求：

### 方案一：轻量版 DMG（推荐）
**文件**: `Dou-flow-1.0.0.dmg`
- **文件大小**: 116KB (极小安装包)
- **架构支持**: Universal Binary (Intel x86_64 + Apple Silicon arm64)
- **系统要求**: macOS 10.15 或更高版本
- **依赖**: 需要用户系统安装 Miniconda/Anaconda
- **优势**: 下载快速，自动管理环境
- **安装方式**: 拖拽安装

### 方案二：自包含版本（开发者版）
**文件**: `Dou-flow.app`（文件夹形式）
- **文件大小**: 3.6GB (包含所有文件)
- **架构支持**: Universal Binary
- **包含内容**: 完整源代码 + AI模型 + 资源文件
- **依赖**: 仍需 conda 环境，但包含所有应用文件
- **优势**: 离线可用，包含完整模型
- **分发方式**: 文件夹拷贝或网盘分享

## 🚀 用户安装指南

### 步骤 1: 下载和挂载
1. 下载 `Dou-flow-1.0.0.dmg` 文件
2. 双击DMG文件进行挂载
3. DMG会自动打开显示安装界面

### 步骤 2: 安装应用
1. 将 `Dou-flow.app` 拖拽到 `Applications` 文件夹
2. 等待复制完成

### 步骤 3: 首次运行
1. 在应用程序文件夹中找到并双击 `Dou-flow.app`
2. 首次运行会自动检测和安装依赖环境（需要网络连接）
3. 系统会提示授予麦克风和辅助功能权限

### 步骤 4: 权限设置
**麦克风权限:**
- 系统设置 → 隐私与安全性 → 麦克风 → 勾选 Dou-flow

**辅助功能权限:**
- 系统设置 → 隐私与安全性 → 辅助功能 → 添加 Dou-flow

## 🎯 功能特点

### Universal Binary 支持
- **Intel Mac**: 原生 x86_64 性能
- **Apple Silicon Mac**: 原生 arm64 性能
- **自动检测**: 系统会自动选择最佳架构运行

### 依赖环境管理
- **自动安装**: 首次运行自动检测并安装 Miniconda
- **隔离环境**: 使用专用的 `wispr-flow-python311` conda 环境
- **依赖管理**: 自动安装所需的 Python 包和 AI 模型

### 安全性
- **代码签名**: 应用包经过 Ad-Hoc 签名
- **权限控制**: 只请求必要的系统权限
- **沙盒兼容**: 兼容 macOS 安全架构

## 🛠 技术规格

### 应用包结构
```
Dou-flow.app/
├── Contents/
│   ├── Info.plist          # 应用元数据和架构信息
│   ├── entitlements.plist  # 权限声明
│   ├── MacOS/
│   │   └── run.command     # 启动脚本
│   └── Resources/
│       └── app_icon.icns   # 应用图标
```

### 启动流程
1. **环境检测**: 检查 conda 和 Python 环境
2. **依赖安装**: 首次运行时自动安装依赖
3. **应用启动**: 激活虚拟环境并启动主程序
4. **错误处理**: 友好的错误提示和自动修复

### 依赖项
- **Python**: 3.11
- **PyQt6**: GUI 框架
- **FunASR**: 中文语音识别引擎
- **PyObjC**: macOS 系统 API 访问
- **其他**: 详见 requirements.txt

## 📋 系统兼容性

### 支持的 macOS 版本
- macOS Catalina 10.15+
- macOS Big Sur 11.0+
- macOS Monterey 12.0+
- macOS Ventura 13.0+
- macOS Sonoma 14.0+
- macOS Sequoia 15.0+

### 处理器架构
- **Intel**: x86_64 (2019年及之前的Mac)
- **Apple Silicon**: arm64 (M1, M2, M3及后续芯片)

## 🔧 故障排除

### 常见问题

**Q: 应用无法启动，提示权限问题**
A: 在系统设置中授予麦克风和辅助功能权限

**Q: 首次安装时间很长**
A: 正常现象，需要下载和安装 AI 模型（约4GB），请保持网络连接

**Q: conda 环境安装失败**
A: 确保有管理员权限，并检查网络连接

**Q: Intel Mac 上运行缓慢**
A: AI 模型在 Apple Silicon 上性能更佳，Intel Mac 运行时间可能较长

### 卸载方法
1. 删除 `/Applications/Dou-flow.app`
2. 删除 conda 环境: `conda env remove -n wispr-flow-python311`
3. 清理用户数据 (可选): `~/Library/Application Support/Dou-flow/`

## 📞 支持与反馈

如遇到问题或需要技术支持，请：
1. 查看应用日志文件
2. 检查系统权限设置
3. 确保网络连接正常
4. 联系开发者或提交问题报告

---

**版本**: 1.0.0  
**构建日期**: 2025-08-05  
**架构**: Universal Binary  
**签名**: Ad-Hoc Signed