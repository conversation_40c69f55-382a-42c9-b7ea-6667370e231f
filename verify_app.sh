#!/bin/bash

# Dou-flow.app 启动验证脚本
# 用于持续修复打包应用的启动问题

set -e

APP_PATH="/Users/<USER>/WorkSpace/Wispr-Flow-CN/Dou-flow.app"
LOG_FILE="/Users/<USER>/WorkSpace/Wispr-Flow-CN/app_error.log"
MAX_ATTEMPTS=10
WAIT_TIME=15

echo "🔧 Dou-flow.app 启动验证脚本"
echo "================================="

# 清理旧的日志文件
cleanup_logs() {
    echo "🧹 清理旧日志文件..."
    if [ -f "$LOG_FILE" ]; then
        rm -f "$LOG_FILE"
    fi
}

# 检查应用是否已经在运行
check_running() {
    pgrep -f "python.*simple_main\.py" > /dev/null 2>&1
    return $?
}

# 强制终止应用
force_quit_app() {
    echo "🛑 强制终止现有应用进程..."
    pkill -f "Dou-flow.app" 2>/dev/null || true
    pkill -f "python src/main.py" 2>/dev/null || true
    sleep 2
}

# 启动应用
start_app() {
    echo "🚀 启动 Dou-flow.app..."
    open "$APP_PATH" &
    LAUNCH_PID=$!
}

# 监控应用启动状态
monitor_startup() {
    local attempt=$1
    local timeout=$WAIT_TIME
    
    echo "⏳ 监控启动状态 (尝试 $attempt/$MAX_ATTEMPTS)..."
    
    # 等待日志文件生成
    while [ $timeout -gt 0 ] && [ ! -f "$LOG_FILE" ]; do
        sleep 1
        timeout=$((timeout - 1))
    done
    
    if [ ! -f "$LOG_FILE" ]; then
        echo "❌ 日志文件未生成，应用可能崩溃了"
        return 1
    fi
    
    # 监控启动过程
    local startup_timeout=30
    while [ $startup_timeout -gt 0 ]; do
        if [ -f "$LOG_FILE" ]; then
            # 检查是否成功进入主事件循环（支持简化版本）
            if grep -q -E "(进入Qt主事件循环|简化应用程序初始化完成，进入主事件循环)" "$LOG_FILE"; then
                # 再等待5秒确保完全初始化
                sleep 5
                if check_running; then
                    echo "✅ 应用启动成功！"
                    return 0
                else
                    echo "❌ 应用启动后立即退出"
                    return 1
                fi
            fi
            
            # 检查是否有崩溃错误
            if grep -q -E "(程序异常退出|退出码|Fatal|Crashed|abort|core dump)" "$LOG_FILE"; then
                echo "❌ 检测到应用崩溃"
                return 1
            fi
        fi
        
        sleep 1
        startup_timeout=$((startup_timeout - 1))
    done
    
    echo "⏰ 启动监控超时"
    return 1
}

# 分析日志并提供修复建议
analyze_logs() {
    echo "📊 分析启动日志..."
    
    if [ ! -f "$LOG_FILE" ]; then
        echo "❌ 无法找到日志文件"
        return 1
    fi
    
    echo "最新日志内容："
    echo "=================="
    tail -20 "$LOG_FILE"
    echo "=================="
    
    # 检查常见错误模式
    if grep -q "Can't instantiate abstract class" "$LOG_FILE"; then
        echo "🔧 发现抽象类实例化错误，需要补充缺失的方法"
        return 2
    elif grep -q "ImportError\|ModuleNotFoundError" "$LOG_FILE"; then
        echo "🔧 发现模块导入错误，需要检查依赖"
        return 3
    elif grep -q "TSMGetInputSourceProperty\|CGEventSourceFlagsState" "$LOG_FILE"; then
        echo "🔧 发现系统API调用错误，需要禁用相关功能"
        return 4
    elif grep -q "Permission denied\|access denied" "$LOG_FILE"; then
        echo "🔧 发现权限错误，需要检查系统权限设置"
        return 5
    elif grep -q "Qt\|PyQt" "$LOG_FILE"; then
        echo "🔧 发现Qt相关错误，需要检查Qt配置"
        return 6
    else
        echo "🤔 未识别的错误模式，需要进一步分析"
        return 1
    fi
}

# 主验证循环
main_verification() {
    for attempt in $(seq 1 $MAX_ATTEMPTS); do
        echo ""
        echo "🔄 第 $attempt 次验证尝试"
        echo "========================"
        
        # 清理环境
        force_quit_app
        cleanup_logs
        
        # 启动应用
        start_app
        
        # 监控启动
        if monitor_startup $attempt; then
            echo ""
            echo "🎉 验证成功！Dou-flow.app 已成功启动并运行"
            echo "应用正在后台运行，可以正常使用"
            return 0
        else
            echo ""
            echo "❌ 第 $attempt 次尝试失败"
            
            # 分析日志
            analyze_logs
            error_type=$?
            
            # 根据错误类型提供修复建议
            case $error_type in
                2)
                    echo "💡 建议：检查 SafeHotkeyManager 类的抽象方法实现"
                    ;;
                3)
                    echo "💡 建议：检查打包环境中的Python路径和模块导入"
                    ;;
                4)
                    echo "💡 建议：确保所有系统API调用都被SafeHotkeyManager屏蔽"
                    ;;
                5)
                    echo "💡 建议：检查应用权限设置，特别是麦克风和辅助功能权限"
                    ;;
                6)
                    echo "💡 建议：检查Qt环境变量和配置"
                    ;;
                *)
                    echo "💡 建议：需要进一步调试分析"
                    ;;
            esac
            
            if [ $attempt -lt $MAX_ATTEMPTS ]; then
                echo "🔄 准备下一次尝试..."
                sleep 3
            fi
        fi
    done
    
    echo ""
    echo "❌ 验证失败：经过 $MAX_ATTEMPTS 次尝试，应用仍无法正常启动"
    echo "请查看日志文件获取详细信息：$LOG_FILE"
    return 1
}

# 脚本入口
echo "开始验证 Dou-flow.app 启动..."
main_verification

exit_code=$?
if [ $exit_code -eq 0 ]; then
    echo ""
    echo "✅ 验证完成：应用启动成功"
else
    echo ""
    echo "❌ 验证失败：应用启动失败"
fi

exit $exit_code