#!/bin/bash

# Dou-flow DMG创建脚本
# 创建Universal DMG安装包，适用于Intel和Apple Silicon Mac

set -e  # 遇到错误立即退出

# 配置变量
APP_NAME="Dou-flow"
APP_VERSION="1.0.0"
DMG_NAME="${APP_NAME}-${APP_VERSION}"
DMG_TEMP_DIR="dmg_temp"
APP_BUNDLE="${APP_NAME}.app"

echo "🎯 开始创建 ${APP_NAME} DMG安装包..."

# 检查应用包是否存在
if [ ! -d "${APP_BUNDLE}" ]; then
    echo "❌ 错误：找不到 ${APP_BUNDLE}"
    echo "请先运行 ./build_dou_flow_app.sh 创建应用包"
    exit 1
fi

# 清理之前的临时文件
echo "🧹 清理临时文件..."
rm -rf "${DMG_TEMP_DIR}"
rm -f "${DMG_NAME}.dmg"

# 创建临时目录
echo "📁 创建DMG临时目录..."
mkdir -p "${DMG_TEMP_DIR}"

# 复制应用包到临时目录
echo "📦 复制应用包..."
cp -R "${APP_BUNDLE}" "${DMG_TEMP_DIR}/"

# 创建应用程序文件夹的符号链接
echo "🔗 创建应用程序文件夹链接..."
ln -s /Applications "${DMG_TEMP_DIR}/Applications"

# 检查并复制README文件（如果存在）
if [ -f "README.md" ]; then
    echo "📄 复制README文件..."
    cp "README.md" "${DMG_TEMP_DIR}/README.txt"
fi

# 设置DMG背景和图标（如果存在）
if [ -f "resources/dmg_background.png" ]; then
    echo "🎨 设置DMG背景..."
    mkdir -p "${DMG_TEMP_DIR}/.background"
    cp "resources/dmg_background.png" "${DMG_TEMP_DIR}/.background/"
fi

# 创建DMG
echo "💿 创建DMG磁盘映像..."

# 估算DMG大小（应用包大小 + 50MB缓冲）
APP_SIZE=$(du -sm "${APP_BUNDLE}" | cut -f1)
DMG_SIZE=$((APP_SIZE + 50))

# 创建临时DMG
hdiutil create -size ${DMG_SIZE}m -fs HFS+ -volname "${APP_NAME}" -srcfolder "${DMG_TEMP_DIR}" "${DMG_NAME}-temp.dmg"

# 转换为最终DMG（压缩格式）
echo "🗜️ 压缩DMG文件..."
hdiutil convert "${DMG_NAME}-temp.dmg" -format UDZO -o "${DMG_NAME}.dmg"

# 清理临时文件
echo "🧹 清理临时文件..."
rm -rf "${DMG_TEMP_DIR}"
rm -f "${DMG_NAME}-temp.dmg"

# 获取DMG文件信息
DMG_SIZE_FINAL=$(du -h "${DMG_NAME}.dmg" | cut -f1)

echo ""
echo "✅ DMG创建完成！"
echo "📦 文件名：${DMG_NAME}.dmg"
echo "📏 文件大小：${DMG_SIZE_FINAL}"
echo "🎯 支持：Intel和Apple Silicon Mac (Universal)"
echo ""
echo "🚀 现在可以分发这个DMG文件给用户安装使用"
echo "💡 用户只需双击DMG文件，然后将 ${APP_NAME}.app 拖拽到应用程序文件夹即可"