# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Wispr Flow CN (Dou-flow) is a macOS voice-to-text application that captures audio via global hotkeys, transcribes speech using FunASR (offline Chinese ASR), and automatically pastes the transcribed text to the current application. The app supports both development and packaged deployment modes.

## Development Environment Setup

### Required Environment
Use conda environment `wispr-flow-python311`:
```bash
conda activate wispr-flow-python311
python src/main.py
```

### Build and Run Commands
```bash
# Development mode (direct Python execution)
conda activate wispr-flow-python311
python src/main.py

# Build packaged application
./build_dou_flow_app.sh

# Launch packaged app
open Dou-flow.app

# Test individual components
python tools/check_dev_permissions.py
python tools/check.py
```

## Architecture Overview

### Core Architecture Pattern
The application follows a **dual-environment architecture** with separate components for development and packaged environments:

- **Development Environment**: Uses standard hotkey/clipboard libraries (pynput, pyperclip)
- **Packaged Environment**: Uses safe alternatives (SafeHotkeyManager, SafeClipboardManager) to avoid macOS security restrictions

### Key Architectural Components

#### 1. Environment Detection System (`src/main.py`, `src/app_loader.py`)
```python
# Environment detection logic
is_packaged = (
    os.environ.get('DISABLE_INPUT_SOURCE_CHECK') == '1' and
    os.environ.get('LAUNCHED_FROM_APP_BUNDLE') == '1'
)
```

#### 2. Factory Pattern for Cross-Environment Compatibility
- `HotkeyManagerFactory`: Creates appropriate hotkey manager based on environment
- `SafeHotkeyManager`: Uses PyObjC (Quartz/AppKit) for packaged apps
- `SafeClipboardManager`: Uses AppleScript for safe clipboard operations

#### 3. Async Component Loading (`src/app_loader.py`)
- `AppLoader`: Handles background initialization of heavy components (FunASR models)
- Progress tracking and splash screen integration
- Thread-safe component management

#### 4. Audio Processing Pipeline
```
AudioCapture -> AudioCaptureThread -> FunASREngine -> ClipboardManager
```

### Critical Environment-Specific Behaviors

#### Packaged App Launch Script (`Dou-flow.app/Contents/MacOS/run.command`)
Sets essential environment variables:
```bash
export DISABLE_INPUT_SOURCE_CHECK=1
export LAUNCHED_FROM_APP_BUNDLE=1
export PYTHONUNBUFFERED=1
export QT_MAC_DISABLE_FOREGROUND_APPLICATION_TRANSFORM=1
```

#### Safe Component Implementation
- **SafeHotkeyManager**: Uses `Quartz.CGEventSourceFlagsState()` and `AppKit.NSEvent.modifierFlags()` for fn key detection
- **SafeClipboardManager**: Uses AppleScript `keystroke "v" using command down` for pasting

## Key Technical Constraints

### macOS Security Limitations
- **Packaged apps cannot use pynput**: Will cause segmentation faults
- **System API access restrictions**: SafeHotkeyManager avoids `TSMGetInputSourceProperty()` calls
- **Permissions required**: Microphone, Accessibility, AppleEvents

### FunASR Integration
- **Model loading**: Heavy initialization (~4GB models) handled asynchronously
- **Offline operation**: Models stored in `src/modelscope/hub/`
- **Chinese optimization**: Optimized for Chinese speech with hotword support

## Common Development Patterns

### Error Handling Pattern
```python
try:
    # Main operation
except Exception as e:
    self.logger.error(f"Operation failed: {e}")
    # Fallback behavior for packaged environment
```

### Thread-Safe Component Access
```python
# Use Qt signals for cross-thread communication
self.component_loaded.emit('component_name', component_object)
```

### Settings Management
- Persistent settings via `SettingsManager`
- Automatic history cleanup
- Environment-specific configuration handling

## Testing and Debugging

### Debug Packaged App Issues
1. Check `app_error.log` for runtime errors
2. Verify environment variables are set correctly
3. Test component initialization sequence via logs
4. Use `verify_app.sh` for systematic validation

### Component Testing
```bash
# Check audio capture
python tools/check.py

# Verify permissions
python tools/check_dev_permissions.py

# Test model loading
python -c "from src.funasr_engine import FunASREngine; engine = FunASREngine()"
```

## Important Implementation Notes

### Hotkey Detection Logic Priority
1. **Development**: Use `pynput` for full key monitoring
2. **Packaged**: Use `PyObjC` with polling-based fn key detection
3. **Fallback**: Disable hotkeys, rely on manual recording button

### Model Management
- FunASR models auto-download on first run
- Models cached in `src/modelscope/hub/`
- Settings track model availability and paths

### Qt Integration Specifics
- **PyQt6** with custom styling system
- **Threading**: UI updates via Qt signals only
- **System tray**: Full macOS integration with menu support

This architecture ensures the app works reliably in both development and deployed environments while maintaining security compliance on macOS.