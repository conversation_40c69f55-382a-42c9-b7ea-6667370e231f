#!/usr/bin/env python3
"""
Dou-flow 安装验证工具
验证应用安装是否正确，所有组件是否正常工作
"""

import os
import sys
import subprocess
import platform
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import json
import time

class InstallationVerifier:
    """安装验证器"""
    
    def __init__(self, app_path: Optional[str] = None):
        """
        初始化验证器
        
        Args:
            app_path: 应用路径，如果为None则自动检测
        """
        self.app_path = app_path or self._find_app_path()
        self.results = {}
        self.errors = []
        self.warnings = []
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def _find_app_path(self) -> Optional[str]:
        """自动查找应用路径"""
        possible_paths = [
            "/Applications/Dou-flow.app",
            os.path.expanduser("~/Applications/Dou-flow.app"),
            "./Dou-flow.app",
            "../Dou-flow.app"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        return None
    
    def verify_system_requirements(self) -> bool:
        """验证系统要求"""
        self.logger.info("验证系统要求...")
        
        # 检查macOS版本
        mac_version = platform.mac_ver()[0]
        if mac_version:
            major, minor = map(int, mac_version.split('.')[:2])
            if major < 10 or (major == 10 and minor < 15):
                self.errors.append(f"macOS版本过低: {mac_version}，需要10.15或更高版本")
                return False
            else:
                self.results['macos_version'] = mac_version
        
        # 检查架构
        arch = platform.machine()
        self.results['architecture'] = arch
        
        # 检查可用磁盘空间
        try:
            statvfs = os.statvfs('/')
            free_space_gb = (statvfs.f_frsize * statvfs.f_bavail) / (1024**3)
            self.results['free_space_gb'] = round(free_space_gb, 2)
            
            if free_space_gb < 4:
                self.warnings.append(f"可用磁盘空间不足: {free_space_gb:.1f}GB，建议至少4GB")
        except Exception as e:
            self.warnings.append(f"无法检查磁盘空间: {e}")
        
        return True
    
    def verify_app_bundle(self) -> bool:
        """验证应用包结构"""
        self.logger.info("验证应用包结构...")
        
        if not self.app_path:
            self.errors.append("未找到Dou-flow.app应用包")
            return False
        
        if not os.path.exists(self.app_path):
            self.errors.append(f"应用包不存在: {self.app_path}")
            return False
        
        # 检查关键文件
        required_files = [
            "Contents/Info.plist",
            "Contents/MacOS/run.command",
            "Contents/Resources/main.py",
            "Contents/Resources/requirements.txt"
        ]
        
        missing_files = []
        for file_path in required_files:
            full_path = os.path.join(self.app_path, file_path)
            if not os.path.exists(full_path):
                missing_files.append(file_path)
        
        if missing_files:
            self.errors.append(f"应用包缺少关键文件: {', '.join(missing_files)}")
            return False
        
        # 检查启动脚本权限
        launch_script = os.path.join(self.app_path, "Contents/MacOS/run.command")
        if not os.access(launch_script, os.X_OK):
            self.errors.append("启动脚本没有执行权限")
            return False
        
        self.results['app_bundle_valid'] = True
        return True
    
    def verify_dependencies(self) -> bool:
        """验证依赖环境"""
        self.logger.info("验证依赖环境...")
        
        # 检查conda
        conda_available = self._check_command_available('conda')
        self.results['conda_available'] = conda_available
        
        if not conda_available:
            self.warnings.append("未检测到conda环境，首次运行时会提示安装")
        else:
            # 检查conda版本
            try:
                result = subprocess.run(['conda', '--version'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    self.results['conda_version'] = result.stdout.strip()
            except Exception as e:
                self.warnings.append(f"无法获取conda版本: {e}")
        
        # 检查git
        git_available = self._check_command_available('git')
        self.results['git_available'] = git_available
        
        if not git_available:
            self.errors.append("未检测到git命令，请安装Xcode Command Line Tools")
            return False
        else:
            # 检查git版本
            try:
                result = subprocess.run(['git', '--version'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    self.results['git_version'] = result.stdout.strip()
            except Exception as e:
                self.warnings.append(f"无法获取git版本: {e}")
        
        return True
    
    def verify_network_connectivity(self) -> bool:
        """验证网络连接"""
        self.logger.info("验证网络连接...")
        
        test_urls = [
            "https://www.modelscope.cn",
            "https://github.com",
            "https://pypi.org"
        ]
        
        connectivity_results = {}
        for url in test_urls:
            try:
                import urllib.request
                urllib.request.urlopen(url, timeout=10)
                connectivity_results[url] = True
            except Exception as e:
                connectivity_results[url] = False
                self.warnings.append(f"无法连接到 {url}: {e}")
        
        self.results['network_connectivity'] = connectivity_results
        
        # 如果所有连接都失败，这是一个错误
        if not any(connectivity_results.values()):
            self.errors.append("网络连接失败，首次运行需要网络连接下载模型")
            return False
        
        return True
    
    def verify_permissions(self) -> bool:
        """验证系统权限"""
        self.logger.info("验证系统权限...")
        
        # 这里只能做基本检查，实际权限需要在运行时检查
        permissions_info = {
            'microphone': '需要在首次运行时授予',
            'accessibility': '需要在首次运行时授予',
            'network': '需要在首次运行时授予'
        }
        
        self.results['permissions_info'] = permissions_info
        self.warnings.append("系统权限需要在首次运行时手动授予")
        
        return True
    
    def verify_model_download_capability(self) -> bool:
        """验证模型下载能力"""
        self.logger.info("验证模型下载能力...")
        
        # 检查模型目录是否存在
        if self.app_path:
            model_dir = os.path.join(self.app_path, "Contents/Resources/src/modelscope/hub/damo")
            if os.path.exists(model_dir):
                self.results['model_directory_exists'] = True
            else:
                self.warnings.append("模型目录不存在，将在首次运行时创建")
        
        # 检查是否有足够空间下载模型
        try:
            statvfs = os.statvfs('/')
            free_space_gb = (statvfs.f_frsize * statvfs.f_bavail) / (1024**3)
            
            if free_space_gb < 2:  # 模型约1.3GB
                self.errors.append(f"磁盘空间不足以下载模型: {free_space_gb:.1f}GB，需要至少2GB")
                return False
        except Exception as e:
            self.warnings.append(f"无法检查磁盘空间: {e}")
        
        return True
    
    def _check_command_available(self, command: str) -> bool:
        """检查命令是否可用"""
        try:
            subprocess.run([command, '--version'], 
                         capture_output=True, timeout=5)
            return True
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.CalledProcessError):
            return False
    
    def run_full_verification(self) -> Dict:
        """运行完整验证"""
        self.logger.info("开始完整安装验证...")
        
        verification_steps = [
            ("系统要求", self.verify_system_requirements),
            ("应用包结构", self.verify_app_bundle),
            ("依赖环境", self.verify_dependencies),
            ("网络连接", self.verify_network_connectivity),
            ("系统权限", self.verify_permissions),
            ("模型下载能力", self.verify_model_download_capability)
        ]
        
        passed_steps = 0
        total_steps = len(verification_steps)
        
        for step_name, step_func in verification_steps:
            try:
                self.logger.info(f"验证 {step_name}...")
                if step_func():
                    passed_steps += 1
                    self.logger.info(f"✅ {step_name} 验证通过")
                else:
                    self.logger.error(f"❌ {step_name} 验证失败")
            except Exception as e:
                self.logger.error(f"❌ {step_name} 验证出错: {e}")
                self.errors.append(f"{step_name} 验证出错: {e}")
        
        # 生成验证报告
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'app_path': self.app_path,
            'total_steps': total_steps,
            'passed_steps': passed_steps,
            'success_rate': round(passed_steps / total_steps * 100, 1),
            'results': self.results,
            'errors': self.errors,
            'warnings': self.warnings,
            'overall_status': 'PASS' if len(self.errors) == 0 else 'FAIL'
        }
        
        return report
    
    def print_report(self, report: Dict):
        """打印验证报告"""
        print("\n" + "="*60)
        print("🔍 Dou-flow 安装验证报告")
        print("="*60)
        print(f"验证时间: {report['timestamp']}")
        print(f"应用路径: {report['app_path'] or '未找到'}")
        print(f"验证步骤: {report['passed_steps']}/{report['total_steps']} ({report['success_rate']}%)")
        print(f"总体状态: {'✅ 通过' if report['overall_status'] == 'PASS' else '❌ 失败'}")
        
        if report['results']:
            print("\n📊 验证结果:")
            for key, value in report['results'].items():
                print(f"  • {key}: {value}")
        
        if report['warnings']:
            print("\n⚠️  警告信息:")
            for warning in report['warnings']:
                print(f"  • {warning}")
        
        if report['errors']:
            print("\n❌ 错误信息:")
            for error in report['errors']:
                print(f"  • {error}")
        
        print("\n💡 建议:")
        if report['overall_status'] == 'PASS':
            print("  • 安装验证通过，可以正常使用应用")
            print("  • 首次运行时请按提示授予必要的系统权限")
        else:
            print("  • 请解决上述错误后重新验证")
            print("  • 如需帮助，请查看安装说明或联系技术支持")
        
        print("="*60)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Dou-flow 安装验证工具')
    parser.add_argument('--app-path', help='指定应用路径')
    parser.add_argument('--output', help='输出报告到JSON文件')
    parser.add_argument('--quiet', action='store_true', help='静默模式')
    
    args = parser.parse_args()
    
    if args.quiet:
        logging.getLogger().setLevel(logging.ERROR)
    
    # 创建验证器并运行验证
    verifier = InstallationVerifier(args.app_path)
    report = verifier.run_full_verification()
    
    # 输出报告
    if not args.quiet:
        verifier.print_report(report)
    
    # 保存报告到文件
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"\n📄 验证报告已保存到: {args.output}")
    
    # 返回适当的退出码
    sys.exit(0 if report['overall_status'] == 'PASS' else 1)


if __name__ == '__main__':
    main()
