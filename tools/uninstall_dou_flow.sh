#!/bin/bash

# Dou-flow 完整卸载工具
# 清理所有相关文件、环境和配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_title() {
    echo -e "${BLUE}$1${NC}"
}

# 确认卸载
confirm_uninstall() {
    echo ""
    print_title "=== Dou-flow 完整卸载工具 ==="
    echo ""
    echo "此工具将完全卸载Dou-flow及其所有相关文件："
    echo ""
    echo "🗑️  将要删除的内容："
    echo "   • 应用程序文件 (/Applications/Dou-flow.app)"
    echo "   • Conda环境 (funasr_env, wispr-flow-python311)"
    if [ "$KEEP_MODELS" != "true" ]; then
        echo "   • 下载的AI模型文件 (~1.3GB)"
    else
        echo "   • AI模型文件将被保留"
    fi
    echo "   • 配置文件和日志"
    echo "   • 用户数据和历史记录"
    echo ""

    if [ "$FORCE_UNINSTALL" = "true" ]; then
        print_warning "强制卸载模式，跳过确认"
    else
        print_warning "此操作不可逆！请确保您真的要完全卸载Dou-flow。"
        echo ""

        read -p "确定要继续卸载吗？(输入 'YES' 确认): " confirm

        if [ "$confirm" != "YES" ]; then
            print_info "卸载已取消"
            exit 0
        fi
    fi

    echo ""
    print_info "开始卸载Dou-flow..."
}

# 删除应用程序
remove_application() {
    print_info "删除应用程序..."
    
    APP_PATHS=(
        "/Applications/Dou-flow.app"
        "$HOME/Applications/Dou-flow.app"
        "./Dou-flow.app"
    )
    
    removed_count=0
    for app_path in "${APP_PATHS[@]}"; do
        if [ -d "$app_path" ]; then
            print_info "删除: $app_path"
            rm -rf "$app_path"
            removed_count=$((removed_count + 1))
        fi
    done
    
    if [ $removed_count -gt 0 ]; then
        print_success "已删除 $removed_count 个应用程序文件"
    else
        print_warning "未找到应用程序文件"
    fi
}

# 删除Conda环境
remove_conda_environments() {
    print_info "删除Conda环境..."
    
    # 检查conda是否可用
    if ! command -v conda >/dev/null 2>&1; then
        print_warning "未检测到conda，跳过环境清理"
        return
    fi
    
    # 要删除的环境列表
    ENVS_TO_REMOVE=(
        "funasr_env"
        "wispr-flow-python311"
        "wispr"
    )
    
    removed_count=0
    for env_name in "${ENVS_TO_REMOVE[@]}"; do
        if conda env list | grep -q "^$env_name "; then
            print_info "删除Conda环境: $env_name"
            conda env remove -n "$env_name" -y 2>/dev/null || print_warning "删除环境 $env_name 失败"
            removed_count=$((removed_count + 1))
        fi
    done
    
    if [ $removed_count -gt 0 ]; then
        print_success "已删除 $removed_count 个Conda环境"
    else
        print_warning "未找到相关Conda环境"
    fi
}

# 删除模型文件
remove_model_files() {
    if [ "$KEEP_MODELS" = "true" ]; then
        print_info "跳过删除AI模型文件（--keep-models选项）"
        return
    fi

    print_info "删除AI模型文件..."

    MODEL_PATHS=(
        "$HOME/.cache/modelscope"
        "$HOME/.cache/huggingface"
        "./src/modelscope"
        "./modelscope"
    )

    removed_size=0
    for model_path in "${MODEL_PATHS[@]}"; do
        if [ -d "$model_path" ]; then
            # 计算大小
            if command -v du >/dev/null 2>&1; then
                size=$(du -sm "$model_path" 2>/dev/null | cut -f1 || echo "0")
                removed_size=$((removed_size + size))
            fi

            print_info "删除模型目录: $model_path"
            rm -rf "$model_path"
        fi
    done

    if [ $removed_size -gt 0 ]; then
        print_success "已删除约 ${removed_size}MB 的模型文件"
    else
        print_warning "未找到模型文件"
    fi
}

# 删除配置文件和日志
remove_config_and_logs() {
    print_info "删除配置文件和日志..."
    
    CONFIG_FILES=(
        "./settings.json"
        "./settings.json.backup"
        "./history.json"
        "./app_error.log"
        "./transcription.log"
        "$HOME/dou-flow-error.log"
        "./settings_history"
        "./logs"
    )
    
    removed_count=0
    for config_file in "${CONFIG_FILES[@]}"; do
        if [ -e "$config_file" ]; then
            print_info "删除: $config_file"
            rm -rf "$config_file"
            removed_count=$((removed_count + 1))
        fi
    done
    
    if [ $removed_count -gt 0 ]; then
        print_success "已删除 $removed_count 个配置/日志文件"
    else
        print_warning "未找到配置文件"
    fi
}

# 删除缓存文件
remove_cache_files() {
    print_info "删除缓存文件..."
    
    CACHE_PATHS=(
        "./__pycache__"
        "./src/__pycache__"
        "./build"
        "./dist"
        "./*.spec"
        "./dmg_temp"
        "./*.dmg"
    )
    
    removed_count=0
    for cache_path in "${CACHE_PATHS[@]}"; do
        if ls $cache_path 1> /dev/null 2>&1; then
            print_info "删除缓存: $cache_path"
            rm -rf $cache_path
            removed_count=$((removed_count + 1))
        fi
    done
    
    if [ $removed_count -gt 0 ]; then
        print_success "已删除缓存文件"
    fi
}

# 清理系统权限（提示用户手动操作）
cleanup_permissions() {
    print_info "清理系统权限..."
    
    echo ""
    print_warning "请手动清理以下系统权限："
    echo ""
    echo "🔧 系统设置 > 隐私与安全性 > 麦克风"
    echo "   • 移除 Dou-flow 或相关终端应用的麦克风权限"
    echo ""
    echo "🔧 系统设置 > 隐私与安全性 > 辅助功能"
    echo "   • 移除 Dou-flow 或相关终端应用的辅助功能权限"
    echo ""
    echo "🔧 系统设置 > 隐私与安全性 > 完全磁盘访问权限"
    echo "   • 检查并移除相关权限（如果有）"
    echo ""
}

# 验证卸载结果
verify_uninstall() {
    print_info "验证卸载结果..."
    
    remaining_files=()
    
    # 检查应用程序
    if [ -d "/Applications/Dou-flow.app" ] || [ -d "$HOME/Applications/Dou-flow.app" ]; then
        remaining_files+=("应用程序文件")
    fi
    
    # 检查Conda环境
    if command -v conda >/dev/null 2>&1; then
        if conda env list | grep -q "funasr_env\|wispr-flow-python311"; then
            remaining_files+=("Conda环境")
        fi
    fi
    
    # 检查配置文件
    if [ -f "./settings.json" ] || [ -f "$HOME/dou-flow-error.log" ]; then
        remaining_files+=("配置文件")
    fi
    
    if [ ${#remaining_files[@]} -eq 0 ]; then
        print_success "卸载完成！所有文件已清理"
    else
        print_warning "以下项目可能未完全清理: ${remaining_files[*]}"
        print_info "您可以手动检查并删除剩余文件"
    fi
}

# 显示卸载总结
show_summary() {
    echo ""
    print_title "=== 卸载完成 ==="
    echo ""
    print_success "Dou-flow 已成功卸载"
    echo ""
    echo "📊 卸载总结："
    echo "   • ✅ 应用程序文件已删除"
    echo "   • ✅ Conda环境已清理"
    echo "   • ✅ AI模型文件已删除"
    echo "   • ✅ 配置文件和日志已清理"
    echo "   • ✅ 缓存文件已删除"
    echo ""
    print_info "感谢您使用Dou-flow！"
    echo ""
    print_warning "如果您将来想要重新安装，请重新下载安装包"
    echo ""
}

# 主函数
main() {
    # 检查是否在正确的目录
    if [ ! -f "README.md" ] && [ ! -f "requirements.txt" ]; then
        print_warning "建议在Dou-flow项目目录中运行此脚本以获得最佳效果"
        echo ""
    fi
    
    # 确认卸载
    confirm_uninstall
    
    # 执行卸载步骤
    remove_application
    remove_conda_environments
    remove_model_files
    remove_config_and_logs
    remove_cache_files
    cleanup_permissions
    
    # 验证和总结
    verify_uninstall
    show_summary
}

# 处理命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --force)
            FORCE_UNINSTALL=true
            shift
            ;;
        --keep-models)
            KEEP_MODELS=true
            shift
            ;;
        --help)
            echo "Dou-flow 卸载工具"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --force        强制卸载，不询问确认"
            echo "  --keep-models  保留AI模型文件"
            echo "  --help         显示此帮助信息"
            echo ""
            exit 0
            ;;
        *)
            print_error "未知选项: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 处理中断信号
trap 'echo ""; print_warning "卸载被中断"; exit 1' INT TERM

# 运行主函数
main "$@"
