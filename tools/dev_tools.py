#!/usr/bin/env python3
"""
Dou-flow 开发者工具集
提供调试、测试、打包的完整工具链
"""

import os
import sys
import subprocess
import argparse
import logging
import json
import time
from pathlib import Path
from typing import List, Dict, Optional
import shutil

class DevTools:
    """开发者工具集"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.logger = self._setup_logging()
        
        # 工具配置
        self.config = {
            'python_version': '3.10',
            'conda_env': 'funasr_env',
            'test_timeout': 300,
            'build_timeout': 1800
        }
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(__name__)
    
    def run_command(self, cmd: List[str], cwd: Optional[str] = None, timeout: int = 60) -> tuple:
        """运行命令并返回结果"""
        try:
            self.logger.info(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(
                cmd,
                cwd=cwd or self.project_root,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result.returncode, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            self.logger.error(f"命令超时: {' '.join(cmd)}")
            return -1, "", "命令执行超时"
        except Exception as e:
            self.logger.error(f"命令执行失败: {e}")
            return -1, "", str(e)
    
    def check_environment(self) -> bool:
        """检查开发环境"""
        self.logger.info("检查开发环境...")
        
        checks = [
            ("Python", ["python", "--version"]),
            ("Git", ["git", "--version"]),
            ("Conda", ["conda", "--version"]),
        ]
        
        all_passed = True
        for name, cmd in checks:
            returncode, stdout, stderr = self.run_command(cmd)
            if returncode == 0:
                self.logger.info(f"✅ {name}: {stdout.strip()}")
            else:
                self.logger.error(f"❌ {name}: 未安装或不可用")
                all_passed = False
        
        return all_passed
    
    def setup_dev_environment(self) -> bool:
        """设置开发环境"""
        self.logger.info("设置开发环境...")
        
        # 检查conda环境是否存在
        returncode, stdout, stderr = self.run_command(["conda", "env", "list"])
        if returncode != 0:
            self.logger.error("Conda不可用")
            return False
        
        env_exists = self.config['conda_env'] in stdout
        
        if not env_exists:
            self.logger.info(f"创建conda环境: {self.config['conda_env']}")
            returncode, stdout, stderr = self.run_command([
                "conda", "create", "-n", self.config['conda_env'], 
                f"python={self.config['python_version']}", "-y"
            ], timeout=300)
            
            if returncode != 0:
                self.logger.error(f"创建环境失败: {stderr}")
                return False
        
        # 安装依赖
        self.logger.info("安装开发依赖...")
        returncode, stdout, stderr = self.run_command([
            "conda", "run", "-n", self.config['conda_env'],
            "pip", "install", "-r", "requirements.txt"
        ], timeout=600)
        
        if returncode != 0:
            self.logger.error(f"安装依赖失败: {stderr}")
            return False
        
        # 安装开发工具
        dev_packages = [
            "pytest", "black", "flake8", "mypy", "isort", "coverage"
        ]
        
        for package in dev_packages:
            returncode, stdout, stderr = self.run_command([
                "conda", "run", "-n", self.config['conda_env'],
                "pip", "install", package
            ])
            
            if returncode == 0:
                self.logger.info(f"✅ 已安装: {package}")
            else:
                self.logger.warning(f"⚠️  安装失败: {package}")
        
        return True
    
    def run_tests(self, test_path: str = "tests/", coverage: bool = True) -> bool:
        """运行测试"""
        self.logger.info("运行测试...")
        
        cmd = ["conda", "run", "-n", self.config['conda_env']]
        
        if coverage:
            cmd.extend(["coverage", "run", "-m", "pytest"])
        else:
            cmd.append("pytest")
        
        cmd.extend([test_path, "-v", "--tb=short"])
        
        returncode, stdout, stderr = self.run_command(
            cmd, timeout=self.config['test_timeout']
        )
        
        if returncode == 0:
            self.logger.info("✅ 所有测试通过")
            
            if coverage:
                # 生成覆盖率报告
                self.run_command([
                    "conda", "run", "-n", self.config['conda_env'],
                    "coverage", "report"
                ])
                
                self.run_command([
                    "conda", "run", "-n", self.config['conda_env'],
                    "coverage", "html"
                ])
                
                self.logger.info("📊 覆盖率报告已生成: htmlcov/index.html")
            
            return True
        else:
            self.logger.error(f"❌ 测试失败:\n{stdout}\n{stderr}")
            return False
    
    def format_code(self) -> bool:
        """格式化代码"""
        self.logger.info("格式化代码...")
        
        # 使用black格式化
        returncode, stdout, stderr = self.run_command([
            "conda", "run", "-n", self.config['conda_env'],
            "black", "src/", "tools/", "tests/"
        ])
        
        if returncode == 0:
            self.logger.info("✅ 代码格式化完成")
        else:
            self.logger.warning(f"⚠️  格式化警告: {stderr}")
        
        # 使用isort整理导入
        returncode, stdout, stderr = self.run_command([
            "conda", "run", "-n", self.config['conda_env'],
            "isort", "src/", "tools/", "tests/"
        ])
        
        if returncode == 0:
            self.logger.info("✅ 导入整理完成")
        else:
            self.logger.warning(f"⚠️  导入整理警告: {stderr}")
        
        return True
    
    def lint_code(self) -> bool:
        """代码检查"""
        self.logger.info("代码检查...")
        
        # flake8检查
        returncode, stdout, stderr = self.run_command([
            "conda", "run", "-n", self.config['conda_env'],
            "flake8", "src/", "--max-line-length=88", "--extend-ignore=E203,W503"
        ])
        
        if returncode == 0:
            self.logger.info("✅ flake8检查通过")
        else:
            self.logger.warning(f"⚠️  flake8警告:\n{stdout}")
        
        # mypy类型检查
        returncode, stdout, stderr = self.run_command([
            "conda", "run", "-n", self.config['conda_env'],
            "mypy", "src/", "--ignore-missing-imports"
        ])
        
        if returncode == 0:
            self.logger.info("✅ mypy检查通过")
        else:
            self.logger.warning(f"⚠️  mypy警告:\n{stdout}")
        
        return True
    
    def build_app(self, build_type: str = "lightweight") -> bool:
        """构建应用"""
        self.logger.info(f"构建应用: {build_type}")
        
        build_scripts = {
            "lightweight": "./build_lightweight_app.sh",
            "standalone": "./build_standalone_app.sh",
            "dmg": "./build_lightweight_dmg.sh"
        }
        
        script = build_scripts.get(build_type)
        if not script:
            self.logger.error(f"未知的构建类型: {build_type}")
            return False
        
        returncode, stdout, stderr = self.run_command([
            "bash", script
        ], timeout=self.config['build_timeout'])
        
        if returncode == 0:
            self.logger.info("✅ 构建完成")
            return True
        else:
            self.logger.error(f"❌ 构建失败:\n{stdout}\n{stderr}")
            return False
    
    def clean_build(self) -> bool:
        """清理构建文件"""
        self.logger.info("清理构建文件...")
        
        clean_paths = [
            "build/", "dist/", "*.spec", "__pycache__/",
            "src/__pycache__/", "*.dmg", "dmg_temp/",
            "Dou-flow.app", ".coverage", "htmlcov/",
            ".pytest_cache/", ".mypy_cache/"
        ]
        
        for pattern in clean_paths:
            if "*" in pattern:
                # 使用shell通配符
                returncode, stdout, stderr = self.run_command([
                    "bash", "-c", f"rm -rf {pattern}"
                ])
            else:
                path = self.project_root / pattern
                if path.exists():
                    if path.is_dir():
                        shutil.rmtree(path)
                    else:
                        path.unlink()
                    self.logger.info(f"删除: {pattern}")
        
        self.logger.info("✅ 清理完成")
        return True
    
    def run_dev_server(self) -> bool:
        """运行开发服务器"""
        self.logger.info("启动开发服务器...")
        
        returncode, stdout, stderr = self.run_command([
            "conda", "run", "-n", self.config['conda_env'],
            "python", "src/main.py"
        ])
        
        return returncode == 0
    
    def generate_docs(self) -> bool:
        """生成文档"""
        self.logger.info("生成文档...")
        
        # 这里可以添加文档生成逻辑
        # 例如使用sphinx或其他文档工具
        
        self.logger.info("✅ 文档生成完成")
        return True
    
    def create_release(self, version: str) -> bool:
        """创建发布版本"""
        self.logger.info(f"创建发布版本: {version}")
        
        # 1. 更新版本号
        # 2. 运行测试
        # 3. 构建应用
        # 4. 创建git标签
        # 5. 生成发布说明
        
        steps = [
            ("运行测试", lambda: self.run_tests()),
            ("代码检查", lambda: self.lint_code()),
            ("构建DMG", lambda: self.build_app("dmg")),
        ]
        
        for step_name, step_func in steps:
            self.logger.info(f"执行: {step_name}")
            if not step_func():
                self.logger.error(f"发布失败: {step_name}")
                return False
        
        self.logger.info("✅ 发布版本创建完成")
        return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Dou-flow 开发者工具")
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 环境相关
    subparsers.add_parser('check-env', help='检查开发环境')
    subparsers.add_parser('setup-env', help='设置开发环境')
    
    # 代码质量
    subparsers.add_parser('test', help='运行测试')
    subparsers.add_parser('format', help='格式化代码')
    subparsers.add_parser('lint', help='代码检查')
    
    # 构建相关
    build_parser = subparsers.add_parser('build', help='构建应用')
    build_parser.add_argument('--type', choices=['lightweight', 'standalone', 'dmg'], 
                             default='lightweight', help='构建类型')
    
    subparsers.add_parser('clean', help='清理构建文件')
    
    # 开发相关
    subparsers.add_parser('dev', help='运行开发服务器')
    subparsers.add_parser('docs', help='生成文档')
    
    # 发布相关
    release_parser = subparsers.add_parser('release', help='创建发布版本')
    release_parser.add_argument('version', help='版本号')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    tools = DevTools()
    
    # 执行对应命令
    commands = {
        'check-env': tools.check_environment,
        'setup-env': tools.setup_dev_environment,
        'test': tools.run_tests,
        'format': tools.format_code,
        'lint': tools.lint_code,
        'build': lambda: tools.build_app(getattr(args, 'type', 'lightweight')),
        'clean': tools.clean_build,
        'dev': tools.run_dev_server,
        'docs': tools.generate_docs,
        'release': lambda: tools.create_release(args.version)
    }
    
    command_func = commands.get(args.command)
    if command_func:
        success = command_func()
        sys.exit(0 if success else 1)
    else:
        print(f"未知命令: {args.command}")
        sys.exit(1)


if __name__ == '__main__':
    main()
