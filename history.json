[{"text": "can you speak english? i want to see some english to check the result for audio translate.", "timestamp": "2025-08-05T02:02:18.641213"}, {"text": "什么情况不知道现在好不好啊？", "timestamp": "2025-08-05T02:02:39.481683"}, {"text": "你好，现在不知道是个什么情况，看看效果。我希望这是一个还不错的效果，最好能够有一个好的表现。同时呢出来的结果。我希望能够。", "timestamp": "2025-08-05T08:12:55.239884"}, {"text": "听好啊，现在验证的效果怎么样，看起来不知道靠不靠谱啊。", "timestamp": "2025-08-05T08:41:39.459319"}, {"text": "现在热键生效了，看起来效果还可以，不知道最后的情况怎么样。我录一段长的语音，不知道它会不会持续的生效，也许还是二十秒的限制。", "timestamp": "2025-08-05T08:46:04.218470"}, {"text": "你好啊，现在录音情况怎么样，不知道结果如何，请给我一些反馈吧。", "timestamp": "2025-08-05T09:03:47.597739"}, {"text": "你好啊，现在测试的效果如何？", "timestamp": "2025-08-05T09:04:49.704855"}, {"text": "看起来现在热键管理有一些问题，直接按热键还是没有办法启动录制。", "timestamp": "2025-08-05T09:09:14.537230"}, {"text": "是的。", "timestamp": "2025-08-05T09:13:22.859457"}, {"text": "现在依然有热件的问题，没法启动热件了，这个方案不是一个好的方案。", "timestamp": "2025-08-05T09:13:42.413918"}, {"text": "ok路径好像恢复了，不知道能不能自动粘贴。", "timestamp": "2025-08-05T09:16:16.413490"}, {"text": "热件好像恢复了，但是自动粘贴好像没有工作。", "timestamp": "2025-08-05T09:16:32.452805"}, {"text": "ok现在正在做测试，看起来测试的效果还ok.", "timestamp": "2025-08-05T09:18:23.933702"}, {"text": "希望整个问题都已经解决了哦，长时间的录音，也许这个问题还没有真正解决，但是我的。", "timestamp": "2025-08-05T09:18:55.156202"}, {"text": "那当前看起来可以做一个gate的commee记录一下，基本功能是vok的。", "timestamp": "2025-08-05T09:19:39.322097"}, {"text": "二二二二十二十十十十十十十十二二二二二二二二二二二二二二二二二二二二二二二二二二二二二二二二二二二二二二二二号号的的的的的一的证据。", "timestamp": "2025-08-05T09:20:21.781371"}, {"text": "a二a零二零二零零二的的的的的的的的的二二二的一的的一的一e.", "timestamp": "2025-08-05T09:20:51.304420"}, {"text": "诶，现在是个什么情况？为什么有一堆乱七八糟的输入？", "timestamp": "2025-08-05T09:22:07.436916"}, {"text": "现在是个什么情况？为什么有一堆乱七八糟的输入？", "timestamp": "2025-08-05T09:22:17.864297"}, {"text": "不知道现在还能不能使用热键的功能应用已经到托盘了。", "timestamp": "2025-08-05T09:23:15.716501"}, {"text": "hello,现在打包看起来已经好了，不知道通用性怎么样。", "timestamp": "2025-08-05T10:41:05.038497"}, {"text": "热键看起来也是可用的，但是不知道粘贴的自动粘贴的效果怎么样？", "timestamp": "2025-08-05T10:41:22.741723"}, {"text": "不知道现在是否还有二十秒的限制。", "timestamp": "2025-08-05T10:41:38.505659"}, {"text": "对对对。", "timestamp": "2025-08-05T10:52:12.247697"}, {"text": "什么情况？莫名其妙的就出问题了。", "timestamp": "2025-08-05T10:52:23.176200"}, {"text": "完整的安装包。", "timestamp": "2025-08-05T10:55:19.318618"}, {"text": "现在是个什么情况，可以使用了吗？不知道靠不靠谱，希望它是靠谱的。", "timestamp": "2025-08-05T11:23:12.654635"}, {"text": "请对项目进行打包呈dmg格式。", "timestamp": "2025-08-05T11:25:09.371317"}, {"text": "用户使用的简洁，尽量的把所有的依赖包都打包进来，其中放asr的模型比较大。", "timestamp": "2025-08-05T11:25:55.662437"}, {"text": "可以设置成首次使用的时候下载。", "timestamp": "2025-08-05T11:26:07.418795"}]